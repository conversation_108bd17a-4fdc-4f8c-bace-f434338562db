/**
 * File Validation Utilities
 * Provides validation functions for file uploads
 */

// File size limits (in bytes)
export const FILE_SIZE_LIMITS = {
  PROFILE_PICTURE: 20 * 1024 * 1024, // 20MB (matches backend limit)
  TASK_ATTACHMENT: 10 * 1024 * 1024, // 10MB
  INSTITUTE_DOCUMENT: 20 * 1024 * 1024, // 20MB (matches API limit)
};

// Allowed file types
export const ALLOWED_FILE_TYPES = {
  IMAGES: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ],
  DOCUMENTS: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/rtf',
    'application/vnd.oasis.opendocument.text'
  ]
};

// File extensions
export const ALLOWED_EXTENSIONS = {
  IMAGES: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  DOCUMENTS: ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt']
};

/**
 * Validate profile picture file
 * @param {File} file - The file to validate
 * @returns {Object} - { isValid: boolean, error: string|null }
 */
export const validateProfilePicture = (file) => {
  if (!file) {
    return { isValid: false, error: 'No file selected' };
  }

  // Check file size
  if (file.size > FILE_SIZE_LIMITS.PROFILE_PICTURE) {
    return { 
      isValid: false, 
      error: `File size must be less than ${FILE_SIZE_LIMITS.PROFILE_PICTURE / (1024 * 1024)}MB` 
    };
  }

  // Check file type
  if (!ALLOWED_FILE_TYPES.IMAGES.includes(file.type)) {
    return { 
      isValid: false, 
      error: `Invalid file type. Allowed types: ${ALLOWED_EXTENSIONS.IMAGES.join(', ')}` 
    };
  }

  return { isValid: true, error: null };
};

/**
 * Validate task attachment file
 * @param {File} file - The file to validate
 * @returns {Object} - { isValid: boolean, error: string|null }
 */
export const validateTaskAttachment = (file) => {
  if (!file) {
    return { isValid: false, error: 'No file selected' };
  }

  // Check file size
  if (file.size > FILE_SIZE_LIMITS.TASK_ATTACHMENT) {
    return { 
      isValid: false, 
      error: `File size must be less than ${FILE_SIZE_LIMITS.TASK_ATTACHMENT / (1024 * 1024)}MB` 
    };
  }

  // Check file type
  const allAllowedTypes = [...ALLOWED_FILE_TYPES.IMAGES, ...ALLOWED_FILE_TYPES.DOCUMENTS];
  if (!allAllowedTypes.includes(file.type)) {
    const allExtensions = [...ALLOWED_EXTENSIONS.IMAGES, ...ALLOWED_EXTENSIONS.DOCUMENTS];
    return { 
      isValid: false, 
      error: `Invalid file type. Allowed types: ${allExtensions.join(', ')}` 
    };
  }

  return { isValid: true, error: null };
};

/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} - Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get file extension from filename
 * @param {string} filename - The filename
 * @returns {string} - File extension (including dot)
 */
export const getFileExtension = (filename) => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * Check if file is an image
 * @param {File} file - The file to check
 * @returns {boolean} - True if file is an image
 */
export const isImageFile = (file) => {
  return ALLOWED_FILE_TYPES.IMAGES.includes(file.type);
};

/**
 * Check if file is a document
 * @param {File} file - The file to check
 * @returns {boolean} - True if file is a document
 */
export const isDocumentFile = (file) => {
  return ALLOWED_FILE_TYPES.DOCUMENTS.includes(file.type);
};

/**
 * Create a preview URL for an image file
 * @param {File} file - The image file
 * @returns {Promise<string>} - Promise that resolves to the preview URL
 */
export const createImagePreview = (file) => {
  return new Promise((resolve, reject) => {
    if (!isImageFile(file)) {
      reject(new Error('File is not an image'));
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (e) => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
};

/**
 * Validate institute document file
 * @param {File} file - The file to validate
 * @returns {Object} - { isValid: boolean, error: string|null }
 */
export const validateInstituteDocument = (file) => {
  if (!file) {
    return { isValid: false, error: 'No file provided' };
  }

  // Check file size (20MB limit for institute documents)
  if (file.size > FILE_SIZE_LIMITS.INSTITUTE_DOCUMENT) {
    return {
      isValid: false,
      error: `File size must be less than ${formatFileSize(FILE_SIZE_LIMITS.INSTITUTE_DOCUMENT)}`
    };
  }

  // Check file type
  if (!ALLOWED_FILE_TYPES.DOCUMENTS.includes(file.type)) {
    const allowedExtensions = ALLOWED_EXTENSIONS.DOCUMENTS.join(', ');
    return {
      isValid: false,
      error: `Invalid file type. Allowed formats: ${allowedExtensions}`
    };
  }

  return { isValid: true, error: null };
};

/**
 * Validate multiple files
 * @param {FileList|Array} files - The files to validate
 * @param {Function} validator - The validation function to use
 * @returns {Object} - { validFiles: Array, invalidFiles: Array, errors: Array }
 */
export const validateMultipleFiles = (files, validator) => {
  const validFiles = [];
  const invalidFiles = [];
  const errors = [];

  Array.from(files).forEach((file, index) => {
    const validation = validator(file);
    if (validation.isValid) {
      validFiles.push(file);
    } else {
      invalidFiles.push(file);
      errors.push(`File ${index + 1} (${file.name}): ${validation.error}`);
    }
  });

  return { validFiles, invalidFiles, errors };
};

export default {
  validateProfilePicture,
  validateTaskAttachment,
  validateInstituteDocument,
  formatFileSize,
  getFileExtension,
  isImageFile,
  isDocumentFile,
  createImagePreview,
  validateMultipleFiles,
  FILE_SIZE_LIMITS,
  ALLOWED_FILE_TYPES,
  ALLOWED_EXTENSIONS
};
