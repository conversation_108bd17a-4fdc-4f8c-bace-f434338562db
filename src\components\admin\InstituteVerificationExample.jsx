import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiHome,
  FiMail,
  FiPhone,
  FiMapPin,
  FiGlobe,
  FiEye,
  FiRefreshCw,
  FiUsers,
  FiCalendar,
  FiCheck,
  FiX,
  FiClock,
  FiFileText
} from 'react-icons/fi';
import {
  fetchPendingVerificationInstitutes,
  fetchInstituteDetailsAdmin,
  selectPendingInstitutes,
  selectPendingLoading,
  selectPendingError,
  selectPendingTotal,
  selectPendingPage,
  selectPendingSize,
  selectPendingHasNext,
  selectPendingHasPrev,
  selectSelectedInstituteProfile,
  selectSelectedInstituteLoading,
  selectSelectedInstituteError,
  clearErrors
} from '../../store/slices/InstituteProfileSlice';
import { LoadingSpinner } from '../ui';

/**
 * Example component demonstrating how to use the new admin institute verification endpoints:
 * 1. GET /api/institutes/admin/pending-verification - Get pending verification institutes with pagination
 * 2. GET /api/institutes/admin/institute/{institute_id} - Get detailed institute information
 */
const InstituteVerificationExample = () => {
  const dispatch = useDispatch();

  // Redux state for pending verification institutes
  const pendingInstitutes = useSelector(selectPendingInstitutes);
  const pendingLoading = useSelector(selectPendingLoading);
  const pendingError = useSelector(selectPendingError);
  const pendingTotal = useSelector(selectPendingTotal);
  const pendingPage = useSelector(selectPendingPage);
  const pendingSize = useSelector(selectPendingSize);
  const pendingHasNext = useSelector(selectPendingHasNext);
  const pendingHasPrev = useSelector(selectPendingHasPrev);

  // Redux state for selected institute details
  const selectedInstitute = useSelector(selectSelectedInstituteProfile);
  const selectedLoading = useSelector(selectSelectedInstituteLoading);
  const selectedError = useSelector(selectSelectedInstituteError);

  // Local state
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  const [selectedInstituteId, setSelectedInstituteId] = useState(null);

  // Load pending verification institutes
  useEffect(() => {
    dispatch(fetchPendingVerificationInstitutes({ 
      skip: currentPage * pageSize, 
      limit: pageSize 
    }));
  }, [dispatch, currentPage, pageSize]);

  // Handle viewing institute details
  const handleViewDetails = (instituteId) => {
    setSelectedInstituteId(instituteId);
    dispatch(fetchInstituteDetailsAdmin(instituteId));
  };

  // Handle pagination
  const handlePreviousPage = () => {
    if (pendingHasPrev) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (pendingHasNext) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Handle page size change
  const handlePageSizeChange = (newSize) => {
    setPageSize(newSize);
    setCurrentPage(0); // Reset to first page
  };

  // Handle refresh
  const handleRefresh = () => {
    dispatch(fetchPendingVerificationInstitutes({ 
      skip: currentPage * pageSize, 
      limit: pageSize 
    }));
  };

  // Clear errors
  const handleClearErrors = () => {
    dispatch(clearErrors());
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Institute Verification Example</h1>
            <p className="text-gray-600 mt-2">
              Demonstrates the new admin institute verification endpoints
            </p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={pendingLoading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <FiRefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Error Display */}
      {(pendingError || selectedError) && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-center justify-between">
            <div className="flex">
              <div className="text-red-400">
                <FiX className="h-5 w-5" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  {pendingError && <p>Pending institutes: {pendingError}</p>}
                  {selectedError && <p>Institute details: {selectedError}</p>}
                </div>
              </div>
            </div>
            <button
              onClick={handleClearErrors}
              className="text-red-400 hover:text-red-600"
            >
              <FiX className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column: Pending Verification Institutes */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              Pending Verification Institutes
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              API: GET /api/institutes/admin/pending-verification
            </p>
          </div>

          <div className="p-6">
            {pendingLoading ? (
              <div className="flex items-center justify-center h-32">
                <LoadingSpinner />
              </div>
            ) : pendingInstitutes.length === 0 ? (
              <div className="text-center py-8">
                <FiUsers className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No pending institutes</h3>
                <p className="mt-1 text-sm text-gray-500">
                  All institutes have been processed.
                </p>
              </div>
            ) : (
              <>
                {/* Statistics */}
                <div className="mb-4 text-sm text-gray-600">
                  Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, pendingTotal)} of {pendingTotal} institutes
                </div>

                {/* Institutes List */}
                <div className="space-y-4">
                  {pendingInstitutes.map((institute) => (
                    <div
                      key={institute.id}
                      className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <FiHome className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-900">
                              {institute.institute_name || 'Unnamed Institute'}
                            </h3>
                            <p className="text-xs text-gray-500">
                              {institute.city}, {institute.state}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <FiClock className="h-3 w-3 mr-1" />
                            {institute.verification_status || 'Pending'}
                          </span>
                          <button
                            onClick={() => handleViewDetails(institute.id)}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            <FiEye className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination Controls */}
                <div className="mt-6 flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handlePreviousPage}
                      disabled={!pendingHasPrev || pendingLoading}
                      className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <span className="text-sm text-gray-700">
                      Page {currentPage + 1}
                    </span>
                    <button
                      onClick={handleNextPage}
                      disabled={!pendingHasNext || pendingLoading}
                      className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      Next
                    </button>
                  </div>

                  <select
                    value={pageSize}
                    onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                    className="border border-gray-300 rounded-md text-sm px-3 py-2"
                  >
                    <option value={10}>10 per page</option>
                    <option value={20}>20 per page</option>
                    <option value={50}>50 per page</option>
                    <option value={100}>100 per page</option>
                  </select>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Right Column: Institute Details */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              Institute Details
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              API: GET /api/institutes/admin/institute/{institute_id}
            </p>
          </div>

          <div className="p-6">
            {!selectedInstituteId ? (
              <div className="text-center py-8">
                <FiFileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No institute selected</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Click the eye icon next to an institute to view details.
                </p>
              </div>
            ) : selectedLoading ? (
              <div className="flex items-center justify-center h-32">
                <LoadingSpinner />
              </div>
            ) : selectedInstitute ? (
              <div className="space-y-6">
                {/* Basic Info */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Basic Information</h3>
                  <div className="grid grid-cols-1 gap-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Institute Name</label>
                      <p className="text-sm text-gray-900">{selectedInstitute.profile?.institute_name || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Type</label>
                      <p className="text-sm text-gray-900">{selectedInstitute.profile?.institute_type || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Verification Status</label>
                      <p className="text-sm text-gray-900">{selectedInstitute.verification_status || 'N/A'}</p>
                    </div>
                  </div>
                </div>

                {/* Statistics */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Statistics</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Total Competitions</label>
                      <p className="text-sm text-gray-900">{selectedInstitute.total_competitions || 0}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Total Mentors</label>
                      <p className="text-sm text-gray-900">{selectedInstitute.total_mentors || 0}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Active Competitions</label>
                      <p className="text-sm text-gray-900">{selectedInstitute.active_competitions || 0}</p>
                    </div>
                  </div>
                </div>

                {/* User Info */}
                {selectedInstitute.user && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Contact Information</h3>
                    <div className="grid grid-cols-1 gap-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Email</label>
                        <p className="text-sm text-gray-900">{selectedInstitute.user.email || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Mobile</label>
                        <p className="text-sm text-gray-900">{selectedInstitute.user.mobile || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Country</label>
                        <p className="text-sm text-gray-900">{selectedInstitute.user.country || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiX className="mx-auto h-12 w-12 text-red-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Failed to load details</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Could not fetch institute details.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstituteVerificationExample;
