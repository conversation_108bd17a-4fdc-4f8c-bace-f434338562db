import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import API_BASE_URL from "../../utils/api/API_URL";

const BASE_URL = `${API_BASE_URL}/api/institutes`;
const getAuthToken = () => localStorage.getItem("token");

// Async Thunks

// Fetch institute profile (includes documents)
export const fetchInstituteProfile = createAsyncThunk(
  "instituteProfile/fetchProfile",
  async (_, thunkAPI) => {
    try {
      // Use the with-documents endpoint to get profile including documents
      const res = await axios.get(`${BASE_URL}/profile/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      // Check if the response contains an error (API returns 200 with error details)
      if (res.data?.detail?.error === 'PROFILE_NOT_CREATED' || res.data?.detail?.error === 'PROFILE_NOT_FOUND') {
        return thunkAPI.rejectWithValue({
          ...res.data,
          isProfileNotFound: true
        });
      }

      return res.data;
    } catch (err) {
      const errorData = err.response?.data || err.message;

      // Handle specific PROFILE_NOT_FOUND error (for backward compatibility)
      if (errorData?.detail?.error === 'PROFILE_NOT_FOUND' || errorData?.detail?.error === 'PROFILE_NOT_CREATED') {
        return thunkAPI.rejectWithValue({
          ...errorData,
          isProfileNotFound: true
        });
      }

      return thunkAPI.rejectWithValue(errorData);
    }
  }
);

// Create/Update institute profile
export const saveInstituteProfile = createAsyncThunk(
  "instituteProfile/saveProfile",
  async (profileData, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/profile`, profileData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json"
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Create/Update institute profile with documents
export const saveInstituteProfileWithDocuments = createAsyncThunk(
  "instituteProfile/saveProfileWithDocuments",
  async ({ profileData, documents }, thunkAPI) => {
    try {
      console.log('Redux Action - saveInstituteProfileWithDocuments:', {
        profileDataKeys: Object.keys(profileData),
        documentsCount: documents.length,
        hasValidProfileData: Object.values(profileData).some(v => v !== null && v !== undefined && v !== '')
      });

      const formData = new FormData();

      // Add profile fields to FormData
      Object.keys(profileData).forEach(key => {
        if (profileData[key] !== null && profileData[key] !== undefined) {
          console.log(`Adding profile field: ${key} = ${profileData[key]}`);
          formData.append(key, profileData[key]);
        }
      });

      // Filter for documents that have actual files to upload
      const validDocuments = documents.filter(doc => doc.file && doc.file instanceof File);

      console.log('Documents received:', documents);
      console.log('Valid documents with files:', validDocuments);

      if (validDocuments.length === 0) {
        // If no valid documents, throw an error since this endpoint requires documents
        throw new Error('No documents provided for document upload endpoint. Use regular profile endpoint instead.');
      }

      // Add document files and metadata using array notation as per API docs
      validDocuments.forEach(doc => {
        console.log('Adding document:', { type: doc.type, description: doc.description, fileName: doc.file?.name });

        // Add the file with array notation
        formData.append('document_files[]', doc.file);

        // Add corresponding metadata with array notation (must match the order and count of files)
        formData.append('document_types[]', doc.type || 'other');
        formData.append('document_descriptions[]', doc.description || '');
      });

      console.log('Added documents to FormData with array notation:', {
        fileCount: validDocuments.length,
        documentDetails: validDocuments.map(doc => ({
          fileName: doc.file.name,
          type: doc.type || 'other',
          description: doc.description || ''
        }))
      });

      // Debug: Log FormData contents
      console.log('FormData contents:');
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      const res = await axios.put(`${BASE_URL}/profile/with-documents`, formData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          // Don't set Content-Type for FormData - browser sets it automatically with boundary
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch profile completion status
export const fetchProfileStatus = createAsyncThunk(
  "instituteProfile/fetchProfileStatus",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/profile/status`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Submit profile for admin approval
export const submitForApproval = createAsyncThunk(
  "instituteProfile/submitForApproval",
  async (_, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/profile/submit-for-verification`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Admin actions - Approve institute profile
export const approveInstituteProfile = createAsyncThunk(
  "instituteProfile/approveProfile",
  async ({ instituteId, approvalData }, thunkAPI) => {
    try {
      const res = await axios.post(`${API_BASE_URL}/api/admin/institutes/${instituteId}/approve`, approvalData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Admin actions - Reject institute profile
export const rejectInstituteProfile = createAsyncThunk(
  "instituteProfile/rejectProfile",
  async ({ instituteId, rejectionData }, thunkAPI) => {
    try {
      const res = await axios.post(`${API_BASE_URL}/api/admin/institutes/${instituteId}/reject`, rejectionData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch pending verification institutes (Admin only) - Updated to match new API
export const fetchPendingVerificationInstitutes = createAsyncThunk(
  "instituteProfile/fetchPendingVerificationInstitutes",
  async ({ skip = 0, limit = 20 } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      const res = await axios.get(`${BASE_URL}/admin/pending-verification?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      console.log('fetchPendingVerificationInstitutes API response:', res.data);

      // Handle the API response format based on the documentation
      const data = res.data;

      return {
        institutes: data.institutes || [],
        total: data.total || 0,
        page: data.page || 0,
        size: data.size || limit,
        has_next: data.has_next || false,
        has_prev: data.has_prev || false
      };
    } catch (err) {
      console.error('fetchPendingVerificationInstitutes error:', err);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Legacy function for backward compatibility - now calls the new function
export const fetchPendingInstitutes = createAsyncThunk(
  "instituteProfile/fetchPendingInstitutes",
  async (params, thunkAPI) => {
    // Delegate to the new function
    return thunkAPI.dispatch(fetchPendingVerificationInstitutes(params)).unwrap();
  }
);

// Fetch detailed institute information (Admin only) - Updated to match new API
export const fetchInstituteDetailsAdmin = createAsyncThunk(
  "instituteProfile/fetchInstituteDetailsAdmin",
  async (instituteId, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/admin/institute/${instituteId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      console.log('fetchInstituteDetailsAdmin API response:', res.data);
      return res.data;
    } catch (err) {
      console.error('fetchInstituteDetailsAdmin error:', err);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Legacy function for backward compatibility
export const fetchInstituteDetails = createAsyncThunk(
  "instituteProfile/fetchInstituteDetails",
  async (instituteId, thunkAPI) => {
    // Delegate to the new function
    return thunkAPI.dispatch(fetchInstituteDetailsAdmin(instituteId)).unwrap();
  }
);

// Fetch all institutes list (Admin only) - Lightweight data for lists
export const fetchAllInstitutesList = createAsyncThunk(
  "instituteProfile/fetchAllInstitutesList",
  async ({ skip = 0, limit = 20, status = "", search = "" } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      if (status) params.append('status', status);
      if (search) params.append('search', search);

      const res = await axios.get(`${API_BASE_URL}/api/admin/institutes/list?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch individual institute profile (Admin only) - Includes documents
export const fetchInstituteProfileById = createAsyncThunk(
  "instituteProfile/fetchInstituteProfileById",
  async (instituteId, thunkAPI) => {
    try {
      const res = await axios.get(`${API_BASE_URL}/api/admin/institutes/${instituteId}/profile`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  profile: null,
  profileLoading: false,
  profileError: null,
  profileNotFound: false, // Flag to track when profile doesn't exist

  saveLoading: false,
  saveError: null,
  saveSuccess: false,

  submitLoading: false,
  submitError: null,
  submitSuccess: false,

  // Admin states - Pending verification institutes
  pendingInstitutes: [],
  pendingLoading: false,
  pendingError: null,
  pendingTotal: 0,
  pendingPage: 0,
  pendingSize: 20,
  pendingHasNext: false,
  pendingHasPrev: false,

  // Admin institute list (lightweight)
  institutesList: [],
  institutesListLoading: false,
  institutesListError: null,
  institutesListTotal: 0,

  // Admin individual institute profile (with documents)
  selectedInstituteProfile: null,
  selectedInstituteLoading: false,
  selectedInstituteError: null,

  approveLoading: false,
  approveError: null,
  approveSuccess: false,

  rejectLoading: false,
  rejectError: null,
  rejectSuccess: false,

  // Profile completion status
  isProfileComplete: false,
  approvalStatus: 'draft', // 'draft', 'pending', 'approved', 'rejected'
  rejectionReason: null,
  approvalDate: null,
  rejectionDate: null
};

// Slice
const instituteProfileSlice = createSlice({
  name: "instituteProfile",
  initialState,
  reducers: {
    clearErrors: (state) => {
      state.profileError = null;
      state.saveError = null;
      state.submitError = null;
      state.pendingError = null;
      state.approveError = null;
      state.rejectError = null;
      state.profileNotFound = false;
    },
    clearSuccessStates: (state) => {
      state.saveSuccess = false;
      state.submitSuccess = false;
      state.approveSuccess = false;
      state.rejectSuccess = false;
    },
    updateProfileField: (state, action) => {
      const { field, value } = action.payload;
      if (state.profile) {
        state.profile[field] = value;
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Profile
      .addCase(fetchInstituteProfile.pending, (state) => {
        state.profileLoading = true;
        state.profileError = null;
        state.profileNotFound = false;
      })
      .addCase(fetchInstituteProfile.fulfilled, (state, action) => {
        state.profileLoading = false;
        state.profileNotFound = false;
        // Handle new API response structure
        const responseData = action.payload;
        state.profile = responseData.profile;
        state.approvalStatus = responseData.verification_status || responseData.profile?.verification_status || 'draft';
        state.rejectionReason = responseData.profile?.verification_notes;
        state.approvalDate = responseData.profile?.verified_at;
        state.isProfileComplete = !!responseData.profile?.institute_name; // Basic check for profile completion
      })
      .addCase(fetchInstituteProfile.rejected, (state, action) => {
        state.profileLoading = false;
        state.profileError = action.payload;

        // Check if this is a PROFILE_NOT_FOUND error
        if (action.payload?.isProfileNotFound) {
          state.profileNotFound = true;
          state.approvalStatus = 'not_created';
          state.isProfileComplete = false;
        }
      })

      // Fetch Profile Status
      .addCase(fetchProfileStatus.pending, (state) => {
        state.profileLoading = true;
        state.profileError = null;
      })
      .addCase(fetchProfileStatus.fulfilled, (state, action) => {
        state.profileLoading = false;
        state.approvalStatus = action.payload || 'draft';
      })
      .addCase(fetchProfileStatus.rejected, (state, action) => {
        state.profileLoading = false;
        state.profileError = action.payload;
      })

      // Save Profile
      .addCase(saveInstituteProfile.pending, (state) => {
        state.saveLoading = true;
        state.saveError = null;
        state.saveSuccess = false;
      })
      .addCase(saveInstituteProfile.fulfilled, (state, action) => {
        state.saveLoading = false;
        state.saveSuccess = true;
        // Handle new API response structure
        const responseData = action.payload;
        state.profile = responseData.profile;
        state.approvalStatus = responseData.verification_status || responseData.profile?.verification_status || 'draft';
        state.isProfileComplete = !!responseData.profile?.institute_name;
      })
      .addCase(saveInstituteProfile.rejected, (state, action) => {
        state.saveLoading = false;
        state.saveError = action.payload;
      })

      // Save Profile with Documents
      .addCase(saveInstituteProfileWithDocuments.pending, (state) => {
        state.saveLoading = true;
        state.saveError = null;
        state.saveSuccess = false;
      })
      .addCase(saveInstituteProfileWithDocuments.fulfilled, (state, action) => {
        state.saveLoading = false;
        state.saveSuccess = true;
        // Handle new API response structure
        const responseData = action.payload;
        state.profile = responseData.profile;
        state.approvalStatus = responseData.verification_status || responseData.profile?.verification_status || 'draft';
        state.isProfileComplete = !!responseData.profile?.institute_name;
      })
      .addCase(saveInstituteProfileWithDocuments.rejected, (state, action) => {
        state.saveLoading = false;
        state.saveError = action.payload;
      })

      // Submit for Approval
      .addCase(submitForApproval.pending, (state) => {
        state.submitLoading = true;
        state.submitError = null;
        state.submitSuccess = false;
      })
      .addCase(submitForApproval.fulfilled, (state, action) => {
        state.submitLoading = false;
        state.submitSuccess = true;
        state.approvalStatus = 'pending';
      })
      .addCase(submitForApproval.rejected, (state, action) => {
        state.submitLoading = false;
        state.submitError = action.payload;
      })

      // Fetch Pending Verification Institutes (Admin) - New API
      .addCase(fetchPendingVerificationInstitutes.pending, (state) => {
        state.pendingLoading = true;
        state.pendingError = null;
      })
      .addCase(fetchPendingVerificationInstitutes.fulfilled, (state, action) => {
        state.pendingLoading = false;
        const payload = action.payload;
        state.pendingInstitutes = payload.institutes || [];
        state.pendingTotal = payload.total || 0;
        state.pendingPage = payload.page || 0;
        state.pendingSize = payload.size || 20;
        state.pendingHasNext = payload.has_next || false;
        state.pendingHasPrev = payload.has_prev || false;
      })
      .addCase(fetchPendingVerificationInstitutes.rejected, (state, action) => {
        state.pendingLoading = false;
        state.pendingError = action.payload;
      })

      // Legacy Fetch Pending Institutes (Admin) - For backward compatibility
      .addCase(fetchPendingInstitutes.pending, (state) => {
        state.pendingLoading = true;
        state.pendingError = null;
      })
      .addCase(fetchPendingInstitutes.fulfilled, (state, action) => {
        state.pendingLoading = false;
        // Handle both old and new response formats
        if (action.payload && typeof action.payload === 'object' && action.payload.institutes) {
          state.pendingInstitutes = action.payload.institutes || [];
          state.pendingTotal = action.payload.total || 0;
          state.pendingPage = action.payload.page || 0;
          state.pendingSize = action.payload.size || 20;
          state.pendingHasNext = action.payload.has_next || false;
          state.pendingHasPrev = action.payload.has_prev || false;
        } else {
          // Legacy format - just an array
          state.pendingInstitutes = Array.isArray(action.payload) ? action.payload : [];
        }
      })
      .addCase(fetchPendingInstitutes.rejected, (state, action) => {
        state.pendingLoading = false;
        state.pendingError = action.payload;
      })

      // Fetch Institute Details Admin (Admin) - New API
      .addCase(fetchInstituteDetailsAdmin.pending, (state) => {
        state.selectedInstituteLoading = true;
        state.selectedInstituteError = null;
      })
      .addCase(fetchInstituteDetailsAdmin.fulfilled, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteProfile = action.payload;
      })
      .addCase(fetchInstituteDetailsAdmin.rejected, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteError = action.payload;
      })

      // Legacy Fetch Institute Details (Admin) - For backward compatibility
      .addCase(fetchInstituteDetails.pending, (state) => {
        state.selectedInstituteLoading = true;
        state.selectedInstituteError = null;
      })
      .addCase(fetchInstituteDetails.fulfilled, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteProfile = action.payload;
      })
      .addCase(fetchInstituteDetails.rejected, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteError = action.payload;
      })

      // Approve Institute (Admin)
      .addCase(approveInstituteProfile.pending, (state) => {
        state.approveLoading = true;
        state.approveError = null;
        state.approveSuccess = false;
      })
      .addCase(approveInstituteProfile.fulfilled, (state, action) => {
        state.approveLoading = false;
        state.approveSuccess = true;
        // Update the institute in pending list
        state.pendingInstitutes = state.pendingInstitutes.filter(
          institute => institute.id !== action.payload.id
        );
      })
      .addCase(approveInstituteProfile.rejected, (state, action) => {
        state.approveLoading = false;
        state.approveError = action.payload;
      })

      // Reject Institute (Admin)
      .addCase(rejectInstituteProfile.pending, (state) => {
        state.rejectLoading = true;
        state.rejectError = null;
        state.rejectSuccess = false;
      })
      .addCase(rejectInstituteProfile.fulfilled, (state, action) => {
        state.rejectLoading = false;
        state.rejectSuccess = true;
        // Update the institute in pending list
        state.pendingInstitutes = state.pendingInstitutes.filter(
          institute => institute.id !== action.payload.id
        );
      })
      .addCase(rejectInstituteProfile.rejected, (state, action) => {
        state.rejectLoading = false;
        state.rejectError = action.payload;
      })

      // Fetch All Institutes List (Admin)
      .addCase(fetchAllInstitutesList.pending, (state) => {
        state.institutesListLoading = true;
        state.institutesListError = null;
      })
      .addCase(fetchAllInstitutesList.fulfilled, (state, action) => {
        state.institutesListLoading = false;
        state.institutesList = action.payload.data || action.payload.institutes || [];
        state.institutesListTotal = action.payload.total || 0;
      })
      .addCase(fetchAllInstitutesList.rejected, (state, action) => {
        state.institutesListLoading = false;
        state.institutesListError = action.payload;
      })

      // Fetch Institute Profile By ID (Admin)
      .addCase(fetchInstituteProfileById.pending, (state) => {
        state.selectedInstituteLoading = true;
        state.selectedInstituteError = null;
      })
      .addCase(fetchInstituteProfileById.fulfilled, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteProfile = action.payload;
      })
      .addCase(fetchInstituteProfileById.rejected, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteError = action.payload;
      });
  },
});

export const { clearErrors, clearSuccessStates, updateProfileField } = instituteProfileSlice.actions;

// Selectors
export const selectProfile = (state) => state.instituteProfile.profile;
export const selectProfileLoading = (state) => state.instituteProfile.profileLoading;
export const selectProfileError = (state) => state.instituteProfile.profileError;
export const selectProfileNotFound = (state) => state.instituteProfile.profileNotFound;
export const selectSaveLoading = (state) => state.instituteProfile.saveLoading;
export const selectSaveError = (state) => state.instituteProfile.saveError;
export const selectSaveSuccess = (state) => state.instituteProfile.saveSuccess;
export const selectSubmitLoading = (state) => state.instituteProfile.submitLoading;
export const selectSubmitError = (state) => state.instituteProfile.submitError;
export const selectSubmitSuccess = (state) => state.instituteProfile.submitSuccess;
export const selectApprovalStatus = (state) => state.instituteProfile.approvalStatus;
export const selectIsProfileComplete = (state) => state.instituteProfile.isProfileComplete;
export const selectRejectionReason = (state) => state.instituteProfile.rejectionReason;

// Admin selectors - Pending verification institutes
export const selectPendingInstitutes = (state) => state.instituteProfile.pendingInstitutes;
export const selectPendingLoading = (state) => state.instituteProfile.pendingLoading;
export const selectPendingError = (state) => state.instituteProfile.pendingError;
export const selectPendingTotal = (state) => state.instituteProfile.pendingTotal;
export const selectPendingPage = (state) => state.instituteProfile.pendingPage;
export const selectPendingSize = (state) => state.instituteProfile.pendingSize;
export const selectPendingHasNext = (state) => state.instituteProfile.pendingHasNext;
export const selectPendingHasPrev = (state) => state.instituteProfile.pendingHasPrev;

// Admin institutes list selectors
export const selectInstitutesList = (state) => state.instituteProfile.institutesList;
export const selectInstitutesListLoading = (state) => state.instituteProfile.institutesListLoading;
export const selectInstitutesListError = (state) => state.instituteProfile.institutesListError;
export const selectInstitutesListTotal = (state) => state.instituteProfile.institutesListTotal;

// Admin selected institute selectors
export const selectSelectedInstituteProfile = (state) => state.instituteProfile.selectedInstituteProfile;
export const selectSelectedInstituteLoading = (state) => state.instituteProfile.selectedInstituteLoading;
export const selectSelectedInstituteError = (state) => state.instituteProfile.selectedInstituteError;

// Admin action selectors
export const selectApproveLoading = (state) => state.instituteProfile.approveLoading;
export const selectApproveSuccess = (state) => state.instituteProfile.approveSuccess;
export const selectRejectLoading = (state) => state.instituteProfile.rejectLoading;
export const selectRejectSuccess = (state) => state.instituteProfile.rejectSuccess;

export default instituteProfileSlice.reducer;
