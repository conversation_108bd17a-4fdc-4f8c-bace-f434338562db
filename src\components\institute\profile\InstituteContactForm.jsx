import React from 'react';
import { FiMail, FiPhone, FiGlobe } from 'react-icons/fi';

const InstituteContactForm = ({
  formData,
  onChange,
  isEditing,
  fieldErrors,
  mandatoryFields,
  hasAttemptedSubmit
}) => {
  const getFieldError = (fieldName) => {
    return hasAttemptedSubmit && fieldErrors[fieldName];
  };

  const isFieldRequired = (fieldName) => {
    return mandatoryFields && mandatoryFields[fieldName];
  };

  const renderField = (fieldName, label, type = 'text', placeholder = '', icon = null) => {
    const error = getFieldError(fieldName);
    const required = isFieldRequired(fieldName);
    
    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <div className="relative">
          {icon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              {React.createElement(icon, { className: "h-4 w-4 text-gray-400" })}
            </div>
          )}
          <input
            type={type}
            name={fieldName}
            value={formData[fieldName] || ''}
            onChange={onChange}
            disabled={!isEditing}
            placeholder={placeholder}
            className={`w-full ${icon ? 'pl-10' : 'pl-3'} pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              !isEditing ? 'bg-gray-50 text-gray-600' : 'bg-white'
            } ${error ? 'border-red-300' : 'border-gray-300'}`}
          />
        </div>
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center">
          <FiMail className="h-5 w-5 text-green-600 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">Contact Information</h3>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Website */}
        {renderField(
          'website',
          'Website',
          'url',
          'https://www.example.edu',
          FiGlobe
        )}

        {/* Phone and Email */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {renderField(
            'phone',
            'Phone Number',
            'tel',
            '+****************',
            FiPhone
          )}
          {renderField(
            'institute_email',
            'Institute Email',
            'email',
            '<EMAIL>',
            FiMail
          )}
        </div>

        {/* Logo and Banner URLs */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {renderField(
            'logo_url',
            'Logo URL',
            'url',
            'https://example.com/logo.png'
          )}
          {renderField(
            'banner_url',
            'Banner URL',
            'url',
            'https://example.com/banner.jpg'
          )}
        </div>

        {/* Preview Section */}
        {(formData.logo_url || formData.banner_url) && (
          <div className="border-t pt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-4">Preview</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Logo Preview */}
              {formData.logo_url && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Logo Preview
                  </label>
                  <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <img
                      src={formData.logo_url}
                      alt="Institute Logo"
                      className="w-24 h-24 object-cover rounded-lg mx-auto"
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'block';
                      }}
                    />
                    <div className="hidden text-center text-sm text-gray-500 mt-2">
                      Invalid image URL
                    </div>
                  </div>
                </div>
              )}

              {/* Banner Preview */}
              {formData.banner_url && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Banner Preview
                  </label>
                  <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <img
                      src={formData.banner_url}
                      alt="Institute Banner"
                      className="w-full h-24 object-cover rounded-lg"
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'block';
                      }}
                    />
                    <div className="hidden text-center text-sm text-gray-500 mt-2">
                      Invalid image URL
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InstituteContactForm;
