import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchMentorProfile,
  fetchMentorProfileCached,
  updateMentorProfile,
  selectIsProfileCacheValid,
  clearProfileCache
} from '../../store/slices/MentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import MentorProfileDisplay from '../../components/mentors/MentorProfileDisplay';

/**
 * Test page to verify mentor profile API integration
 * This component demonstrates how to fetch and display mentor profile data
 * using the new API response structure
 */
const MentorProfileTest = () => {
  const dispatch = useDispatch();
  const { myProfile, profileLoading, profileError } = useSelector(state => state.mentors);
  const isCacheValid = useSelector(selectIsProfileCacheValid);
  const [rawResponse, setRawResponse] = useState(null);
  const [lastFetchTime, setLastFetchTime] = useState(null);
  const [fetchMethod, setFetchMethod] = useState('cached');

  useEffect(() => {
    // Fetch mentor profile on component mount using cached version
    const fetchProfile = async () => {
      try {
        const result = await dispatch(fetchMentorProfileCached()).unwrap();
        setRawResponse(result);
        setLastFetchTime(new Date().toLocaleTimeString());
        setFetchMethod('cached');
        console.log('Raw API Response (cached):', result);
      } catch (error) {
        console.error('Error fetching mentor profile:', error);
      }
    };

    fetchProfile();
  }, [dispatch]);

  const handleUpdateProfile = async () => {
    try {
      const updateData = {
        bio: "Updated bio from test page - " + new Date().toISOString(),
        experience_years: 5,
        hourly_rate: 75.00
      };

      const result = await dispatch(updateMentorProfile(updateData)).unwrap();
      setRawResponse(result);
      setLastFetchTime(new Date().toLocaleTimeString());
      setFetchMethod('update');
      console.log('Update Response:', result);
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating mentor profile:', error);
      alert('Error updating profile: ' + (error.message || 'Unknown error'));
    }
  };

  const handleFetchCached = async () => {
    try {
      const result = await dispatch(fetchMentorProfileCached()).unwrap();
      setRawResponse(result);
      setLastFetchTime(new Date().toLocaleTimeString());
      setFetchMethod('cached');
      console.log('Cached fetch response:', result);
    } catch (error) {
      console.error('Error fetching cached profile:', error);
    }
  };

  const handleFetchFresh = async () => {
    try {
      const result = await dispatch(fetchMentorProfile()).unwrap();
      setRawResponse(result);
      setLastFetchTime(new Date().toLocaleTimeString());
      setFetchMethod('fresh');
      console.log('Fresh fetch response:', result);
    } catch (error) {
      console.error('Error fetching fresh profile:', error);
    }
  };

  const handleClearCache = () => {
    dispatch(clearProfileCache());
    setLastFetchTime(new Date().toLocaleTimeString());
    setFetchMethod('cache cleared');
    console.log('Profile cache cleared');
  };

  if (profileLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (profileError) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <ErrorMessage message={profileError} />
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Mentor Profile API Test with Caching</h1>
        <p className="text-gray-600 mb-4">
          This page demonstrates the mentor profile API integration with caching functionality.
        </p>

        {/* Cache Status */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Cache Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">Cache Valid: </span>
              <span className={isCacheValid ? 'text-green-600' : 'text-red-600'}>
                {isCacheValid ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">Last Fetch: </span>
              <span className="text-gray-600">{lastFetchTime || 'Never'}</span>
            </div>
            <div>
              <span className="font-medium">Method: </span>
              <span className="text-gray-600">{fetchMethod}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
          <button
            onClick={handleFetchCached}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Fetch (Cached)
          </button>

          <button
            onClick={handleFetchFresh}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            Fetch (Fresh)
          </button>

          <button
            onClick={handleClearCache}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            Clear Cache
          </button>

          <button
            onClick={handleUpdateProfile}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            Update Profile
          </button>

          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Reload Page
          </button>
        </div>
      </div>

      {/* Profile Display */}
      {myProfile && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Processed Profile Display</h2>
            <MentorProfileDisplay mentorData={myProfile} showFullDetails={true} />
          </div>
          
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Redux Store Data</h2>
            <div className="bg-gray-50 rounded-lg p-4 overflow-auto max-h-96">
              <pre className="text-xs text-gray-800">
                {JSON.stringify(myProfile, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      )}

      {/* Raw API Response */}
      {rawResponse && (
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Raw API Response</h2>
          <div className="bg-gray-50 rounded-lg p-4 overflow-auto max-h-96">
            <pre className="text-xs text-gray-800">
              {JSON.stringify(rawResponse, null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* API Structure Documentation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Expected API Response Structure</h2>
        <div className="bg-gray-50 rounded-lg p-4">
          <pre className="text-xs text-gray-800">
{`{
  "user": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "mobile": "string",
    "country": "string",
    "profile_picture": "string",
    "user_type": "mentor",
    "is_email_verified": boolean,
    "is_mobile_verified": boolean,
    "created_at": "datetime",
    "mentor_profile": {
      "id": "uuid",
      "user_id": "uuid",
      "bio": "string",
      "experience_years": number,
      "hourly_rate": "decimal",
      "languages": ["string"],
      "availability_hours": {
        "monday": ["09:00-12:00"],
        "tuesday": ["09:00-12:00"],
        ...
      },
      "profile_image_url": "string",
      "expertise_subjects": [
        {"id": "uuid", "name": "string"}
      ],
      "preferred_subjects": [
        {"id": "uuid", "name": "string"}
      ],
      "created_at": "datetime",
      "updated_at": "datetime"
    }
  },
  "profile": {
    // Same structure as user.mentor_profile
  },
  "total_competitions": number,
  "active_institutes": number,
  "average_rating": number,
  "verification_status": "pending|verified|rejected"
}`}
          </pre>
        </div>
      </div>

      {/* Update Profile Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Profile Update</h2>
        <p className="text-gray-600 mb-4">
          Use the "Test Update Profile" button above to test the update functionality.
          This will update the bio with a timestamp and set experience_years to 5 and hourly_rate to $75.
        </p>
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Caching Notes
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>Profile data is cached for 5 minutes after a successful API call</li>
                  <li>"Fetch (Cached)" will use cached data if valid, otherwise fetch fresh data</li>
                  <li>"Fetch (Fresh)" always makes a new API call and updates the cache</li>
                  <li>"Clear Cache" removes cached data, forcing next fetch to be fresh</li>
                  <li>Profile updates automatically refresh the cache</li>
                  <li>Make sure you're authenticated as a mentor to test this functionality</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MentorProfileTest;
