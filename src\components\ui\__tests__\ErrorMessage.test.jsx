import React from 'react';
import { render, screen } from '@testing-library/react';
import ErrorMessage from '../ErrorMessage';

describe('ErrorMessage Component', () => {
  test('should render string error without issues', () => {
    render(<ErrorMessage error="Simple error message" />);
    expect(screen.getByText('Simple error message')).toBeInTheDocument();
  });

  test('should render error object with detail property safely', () => {
    const errorWithObjectDetail = {
      detail: {
        message: 'Nested error message',
        code: 'VALIDATION_ERROR'
      }
    };
    
    // This should NOT throw "Objects are not valid as a React child" error
    render(<ErrorMessage error={errorWithObjectDetail} />);
    
    // Should render the stringified version of the detail object
    expect(screen.getByText(/VALIDATION_ERROR/)).toBeInTheDocument();
    expect(screen.getByText(/Nested error message/)).toBeInTheDocument();
  });

  test('should render error object with string detail property', () => {
    const errorWithStringDetail = {
      detail: 'String detail message'
    };
    
    render(<ErrorMessage error={errorWithStringDetail} />);
    expect(screen.getByText('String detail message')).toBeInTheDocument();
  });

  test('should render error object with message property', () => {
    const errorWithMessage = {
      message: 'Error message property'
    };
    
    render(<ErrorMessage error={errorWithMessage} />);
    expect(screen.getByText('Error message property')).toBeInTheDocument();
  });

  test('should handle complex nested error objects', () => {
    const complexError = {
      detail: {
        errors: [
          { field: 'email', message: 'Invalid email' },
          { field: 'password', message: 'Too short' }
        ],
        code: 'VALIDATION_FAILED'
      }
    };
    
    // This should render without throwing React errors
    render(<ErrorMessage error={complexError} />);
    
    // Should contain the stringified content
    expect(screen.getByText(/VALIDATION_FAILED/)).toBeInTheDocument();
  });

  test('should handle null/undefined errors gracefully', () => {
    render(<ErrorMessage error={null} />);
    expect(screen.getByText('An error occurred')).toBeInTheDocument();
    
    render(<ErrorMessage error={undefined} />);
    expect(screen.getByText('An error occurred')).toBeInTheDocument();
  });
});
