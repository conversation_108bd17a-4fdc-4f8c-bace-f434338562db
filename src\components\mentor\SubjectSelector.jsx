import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiSearch, FiX, FiCheck, FiPlus } from 'react-icons/fi';
import { fetchSubjects } from '../../store/slices/SubjectSlice';

const SubjectSelector = ({ 
  selectedSubjectIds = [], 
  onSelectionChange, 
  label = "Select Subjects",
  placeholder = "Search subjects...",
  maxSelections = null,
  className = ""
}) => {
  const dispatch = useDispatch();
  const { subjects, loading } = useSelector(state => state.subjects);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Fetch subjects on component mount
  useEffect(() => {
    if (subjects.length === 0) {
      dispatch(fetchSubjects({ skip: 0, limit: 1000 }));
    }
  }, [dispatch, subjects.length]);

  // Filter subjects based on search term
  const filteredSubjects = subjects.filter(subject =>
    subject.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get selected subjects
  const selectedSubjects = subjects.filter(subject => 
    selectedSubjectIds.includes(subject.id)
  );

  // Handle subject selection
  const handleSubjectToggle = useCallback((subjectId) => {
    let newSelection;
    
    if (selectedSubjectIds.includes(subjectId)) {
      // Remove subject
      newSelection = selectedSubjectIds.filter(id => id !== subjectId);
    } else {
      // Add subject (check max limit)
      if (maxSelections && selectedSubjectIds.length >= maxSelections) {
        return; // Don't add if max reached
      }
      newSelection = [...selectedSubjectIds, subjectId];
    }
    
    onSelectionChange(newSelection);
  }, [selectedSubjectIds, onSelectionChange, maxSelections]);

  // Remove selected subject
  const handleRemoveSubject = useCallback((subjectId) => {
    const newSelection = selectedSubjectIds.filter(id => id !== subjectId);
    onSelectionChange(newSelection);
  }, [selectedSubjectIds, onSelectionChange]);

  return (
    <div className={`relative ${className}`}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
        {maxSelections && (
          <span className="text-gray-500 ml-1">
            ({selectedSubjectIds.length}/{maxSelections})
          </span>
        )}
      </label>

      {/* Selected Subjects Display */}
      {selectedSubjects.length > 0 && (
        <div className="mb-3 flex flex-wrap gap-2">
          {selectedSubjects.map(subject => (
            <div
              key={subject.id}
              className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
            >
              <span>{subject.name}</span>
              <button
                type="button"
                onClick={() => handleRemoveSubject(subject.id)}
                className="ml-2 text-blue-600 hover:text-blue-800 transition-colors"
              >
                <FiX className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Search Input */}
      <div className="relative">
        <div className="relative">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setIsDropdownOpen(true)}
            placeholder={placeholder}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Dropdown */}
        {isDropdownOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-gray-500">
                Loading subjects...
              </div>
            ) : filteredSubjects.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                {searchTerm ? 'No subjects found' : 'No subjects available'}
              </div>
            ) : (
              <div className="py-1">
                {filteredSubjects.map(subject => {
                  const isSelected = selectedSubjectIds.includes(subject.id);
                  const isDisabled = !isSelected && maxSelections && selectedSubjectIds.length >= maxSelections;
                  
                  return (
                    <button
                      key={subject.id}
                      type="button"
                      onClick={() => !isDisabled && handleSubjectToggle(subject.id)}
                      disabled={isDisabled}
                      className={`w-full text-left px-4 py-2 text-sm transition-colors ${
                        isSelected
                          ? 'bg-blue-50 text-blue-700'
                          : isDisabled
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span>{subject.name}</span>
                        {isSelected && (
                          <FiCheck className="h-4 w-4 text-blue-600" />
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Click outside to close dropdown */}
      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}

      {/* Help text */}
      <p className="mt-1 text-xs text-gray-500">
        {maxSelections 
          ? `Select up to ${maxSelections} subjects`
          : 'Select multiple subjects by searching and clicking'
        }
      </p>
    </div>
  );
};

export default SubjectSelector;
