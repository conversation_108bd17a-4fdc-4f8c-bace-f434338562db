import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiHome,
  FiMail,
  FiPhone,
  FiMapPin,
  FiGlobe,
  FiCheck,
  FiX,
  FiEye,
  FiClock,
  FiRefreshCw,
  FiAlertCircle,
  FiUsers,
  FiCalendar
} from 'react-icons/fi';
import {
  fetchPendingInstitutes,
  approveInstituteProfile,
  rejectInstituteProfile,
  selectPendingInstitutes,
  selectPendingLoading,
  selectPendingError,
  selectApproveLoading,
  selectApproveSuccess,
  selectRejectLoading,
  selectRejectSuccess,
  clearErrors,
  clearSuccessStates
} from '../../store/slices/InstituteProfileSlice';
import { LoadingSpinner } from '../../components/ui';

const InstituteApprovals = () => {
  const dispatch = useDispatch();
  
  // Redux state
  const pendingInstitutesRaw = useSelector(selectPendingInstitutes);
  const pendingInstitutes = Array.isArray(pendingInstitutesRaw) ? pendingInstitutesRaw : [];
  const pendingLoading = useSelector(selectPendingLoading);
  const pendingError = useSelector(selectPendingError);
  const approveLoading = useSelector(selectApproveLoading);
  const approveSuccess = useSelector(selectApproveSuccess);
  const rejectLoading = useSelector(selectRejectLoading);
  const rejectSuccess = useSelector(selectRejectSuccess);

  // Local state
  const [selectedInstitute, setSelectedInstitute] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(''); // 'approve', 'reject', 'view'
  const [rejectionReason, setRejectionReason] = useState('');
  const [approvalNotes, setApprovalNotes] = useState('');

  // Load pending institutes on component mount
  useEffect(() => {
    dispatch(fetchPendingInstitutes());
  }, [dispatch]);

  // Handle success states
  useEffect(() => {
    if (approveSuccess || rejectSuccess) {
      setShowModal(false);
      setSelectedInstitute(null);
      setRejectionReason('');
      setApprovalNotes('');
      dispatch(fetchPendingInstitutes()); // Refresh the list
      
      const timer = setTimeout(() => {
        dispatch(clearSuccessStates());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [approveSuccess, rejectSuccess, dispatch]);

  // Handle approve
  const handleApprove = async () => {
    if (!selectedInstitute) return;
    
    try {
      await dispatch(approveInstituteProfile({
        instituteId: selectedInstitute.id,
        approvalData: {
          notes: approvalNotes,
          approved_at: new Date().toISOString()
        }
      })).unwrap();
    } catch (error) {
      console.error('Failed to approve institute:', error);
    }
  };

  // Handle reject
  const handleReject = async () => {
    if (!selectedInstitute || !rejectionReason.trim()) {
      alert('Please provide a reason for rejection.');
      return;
    }
    
    try {
      await dispatch(rejectInstituteProfile({
        instituteId: selectedInstitute.id,
        rejectionData: {
          reason: rejectionReason,
          rejected_at: new Date().toISOString()
        }
      })).unwrap();
    } catch (error) {
      console.error('Failed to reject institute:', error);
    }
  };

  // Open modal
  const openModal = (institute, type) => {
    setSelectedInstitute(institute);
    setModalType(type);
    setShowModal(true);
  };

  // Close modal
  const closeModal = () => {
    setShowModal(false);
    setSelectedInstitute(null);
    setModalType('');
    setRejectionReason('');
    setApprovalNotes('');
  };

  if (pendingLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Institute Approvals</h1>
            <p className="text-gray-600 mt-2">
              Review and approve institute profile submissions
            </p>
          </div>
          <button
            onClick={() => dispatch(fetchPendingInstitutes())}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiRefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Success Messages */}
      {(approveSuccess || rejectSuccess) && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <FiCheck className="h-5 w-5 text-green-600 mr-3" />
            <p className="text-green-700">
              {approveSuccess && 'Institute approved successfully!'}
              {rejectSuccess && 'Institute rejected successfully!'}
            </p>
          </div>
        </div>
      )}

      {/* Error Message */}
      {pendingError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <FiAlertCircle className="h-5 w-5 text-red-600 mr-3" />
            <p className="text-red-700">{pendingError}</p>
          </div>
        </div>
      )}

      {/* Pending Institutes List */}
      {pendingInstitutes.length === 0 ? (
        <div className="text-center py-12">
          <FiHome className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No pending approvals</h3>
          <p className="mt-1 text-sm text-gray-500">
            All institute profiles have been reviewed.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pendingInstitutes.map((institute) => (
            <div key={institute.id} className="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <FiHome className="h-8 w-8 text-blue-600 mr-3" />
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {institute.profile?.institute_name || 'Unnamed Institute'}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {institute.profile?.institute_type || 'Unknown Type'}
                      </p>
                    </div>
                  </div>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <FiClock className="h-3 w-3 mr-1" />
                    Pending
                  </span>
                </div>

                {/* Basic Info */}
                <div className="space-y-2 mb-4">
                  {institute.profile?.contact_email && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FiMail className="h-4 w-4 mr-2" />
                      {institute.profile.contact_email}
                    </div>
                  )}
                  {institute.profile?.contact_phone && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FiPhone className="h-4 w-4 mr-2" />
                      {institute.profile.contact_phone}
                    </div>
                  )}
                  {institute.profile?.address?.city && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FiMapPin className="h-4 w-4 mr-2" />
                      {institute.profile.address.city}, {institute.profile.address.country}
                    </div>
                  )}
                  {institute.profile?.established_year && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FiCalendar className="h-4 w-4 mr-2" />
                      Established {institute.profile.established_year}
                    </div>
                  )}
                </div>

                {/* Description */}
                {institute.profile?.description && (
                  <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                    {institute.profile.description}
                  </p>
                )}

                {/* Actions */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => openModal(institute, 'view')}
                    className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <FiEye className="h-4 w-4 mr-1" />
                    View
                  </button>
                  <button
                    onClick={() => openModal(institute, 'approve')}
                    disabled={approveLoading}
                    className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    <FiCheck className="h-4 w-4 mr-1" />
                    Approve
                  </button>
                  <button
                    onClick={() => openModal(institute, 'reject')}
                    disabled={rejectLoading}
                    className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    <FiX className="h-4 w-4 mr-1" />
                    Reject
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default InstituteApprovals;
