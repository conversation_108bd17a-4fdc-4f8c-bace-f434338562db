import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiHome,
  FiMail,
  FiPhone,
  FiMapPin,
  FiGlobe,
  FiCheck,
  FiX,
  FiEye,
  FiClock,
  FiRefreshCw,
  FiAlertCircle,
  FiUsers,
  FiCalendar,
  FiFileText
} from 'react-icons/fi';
import {
  fetchPendingVerificationInstitutes,
  fetchPendingInstitutes, // Keep for backward compatibility
  fetchInstituteDetailsAdmin,
  approveInstituteProfile,
  rejectInstituteProfile,
  selectPendingInstitutes,
  selectPendingLoading,
  selectPendingError,
  selectPendingTotal,
  selectPendingPage,
  selectPendingSize,
  selectPendingHasNext,
  selectPendingHasPrev,
  selectApproveLoading,
  selectApproveSuccess,
  selectRejectLoading,
  selectRejectSuccess,
  clearErrors,
  clearSuccessStates
} from '../../store/slices/InstituteProfileSlice';
import { LoadingSpinner } from '../../components/ui';

const InstituteApprovals = () => {
  const dispatch = useDispatch();
  
  // Redux state
  const pendingInstitutesRaw = useSelector(selectPendingInstitutes);
  const pendingInstitutes = Array.isArray(pendingInstitutesRaw) ? pendingInstitutesRaw : [];
  const pendingLoading = useSelector(selectPendingLoading);
  const pendingError = useSelector(selectPendingError);
  const pendingTotal = useSelector(selectPendingTotal);
  const pendingPage = useSelector(selectPendingPage);
  const pendingSize = useSelector(selectPendingSize);
  const pendingHasNext = useSelector(selectPendingHasNext);
  const pendingHasPrev = useSelector(selectPendingHasPrev);
  const approveLoading = useSelector(selectApproveLoading);
  const approveSuccess = useSelector(selectApproveSuccess);
  const rejectLoading = useSelector(selectRejectLoading);
  const rejectSuccess = useSelector(selectRejectSuccess);

  // Local state
  const [selectedInstitute, setSelectedInstitute] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(''); // 'approve', 'reject', 'view'
  const [rejectionReason, setRejectionReason] = useState('');
  const [approvalNotes, setApprovalNotes] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(20);

  // Load pending institutes on component mount and when pagination changes
  useEffect(() => {
    dispatch(fetchPendingVerificationInstitutes({
      skip: currentPage * pageSize,
      limit: pageSize
    }));
  }, [dispatch, currentPage, pageSize]);

  // Handle success states
  useEffect(() => {
    if (approveSuccess || rejectSuccess) {
      setShowModal(false);
      setSelectedInstitute(null);
      setRejectionReason('');
      setApprovalNotes('');
      dispatch(fetchPendingVerificationInstitutes({
        skip: currentPage * pageSize,
        limit: pageSize
      })); // Refresh the list
      
      const timer = setTimeout(() => {
        dispatch(clearSuccessStates());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [approveSuccess, rejectSuccess, dispatch]);

  // Handle approve
  const handleApprove = async () => {
    if (!selectedInstitute) return;
    
    try {
      await dispatch(approveInstituteProfile({
        instituteId: selectedInstitute.id,
        approvalData: {
          notes: approvalNotes,
          approved_at: new Date().toISOString()
        }
      })).unwrap();
    } catch (error) {
      console.error('Failed to approve institute:', error);
    }
  };

  // Handle reject
  const handleReject = async () => {
    if (!selectedInstitute || !rejectionReason.trim()) {
      alert('Please provide a reason for rejection.');
      return;
    }
    
    try {
      await dispatch(rejectInstituteProfile({
        instituteId: selectedInstitute.id,
        rejectionData: {
          reason: rejectionReason,
          rejected_at: new Date().toISOString()
        }
      })).unwrap();
    } catch (error) {
      console.error('Failed to reject institute:', error);
    }
  };

  // Fetch detailed institute information
  const fetchInstituteDetails = async (instituteId) => {
    try {
      const details = await dispatch(fetchInstituteDetailsAdmin(instituteId)).unwrap();
      return details;
    } catch (error) {
      console.error('Failed to fetch institute details:', error);
      return null;
    }
  };

  // Open modal
  const openModal = async (institute, type) => {
    console.log('Opening modal for institute:', institute);
    console.log('Modal type:', type);

    setSelectedInstitute(institute);
    setModalType(type);
    setShowModal(true);

    // If viewing details, fetch complete institute information using the detailed API
    if (type === 'view') {
      console.log('Fetching detailed institute data for ID:', institute.id);
      try {
        const detailedData = await dispatch(fetchInstituteDetailsAdmin(institute.id)).unwrap();
        console.log('Fetched detailed institute data:', detailedData);
        setSelectedInstitute(detailedData);
      } catch (error) {
        console.error('Failed to fetch detailed institute data:', error);
        // Keep the basic institute data if detailed fetch fails
      }
    }
  };

  // Close modal
  const closeModal = () => {
    setShowModal(false);
    setSelectedInstitute(null);
    setModalType('');
    setRejectionReason('');
    setApprovalNotes('');
  };

  if (pendingLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Institute Approvals</h1>
            <p className="text-gray-600 mt-2">
              Review and approve institute profile submissions
            </p>
          </div>
          <button
            onClick={() => dispatch(fetchPendingVerificationInstitutes({
              skip: currentPage * pageSize,
              limit: pageSize
            }))}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiRefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Success Messages */}
      {(approveSuccess || rejectSuccess) && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <FiCheck className="h-5 w-5 text-green-600 mr-3" />
            <p className="text-green-700">
              {approveSuccess && 'Institute approved successfully!'}
              {rejectSuccess && 'Institute rejected successfully!'}
            </p>
          </div>
        </div>
      )}

      {/* Error Message */}
      {pendingError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <FiAlertCircle className="h-5 w-5 text-red-600 mr-3" />
            <p className="text-red-700">{pendingError}</p>
          </div>
        </div>
      )}

      {/* Pending Institutes List */}
      {pendingInstitutes.length === 0 ? (
        <div className="text-center py-12">
          <FiHome className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No pending approvals</h3>
          <p className="mt-1 text-sm text-gray-500">
            All institute profiles have been reviewed.
          </p>
        </div>
      ) : (
        <>
          {/* Table */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Institute
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {pendingInstitutes.map((institute) => (
                  <tr key={institute.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          {institute.profile_picture ? (
                            <img
                              className="h-10 w-10 rounded-full object-cover"
                              src={institute.profile_picture}
                              alt={institute.institute_name}
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              <FiHome className="h-5 w-5 text-gray-500" />
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {institute.institute_name || 'Unnamed Institute'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {institute.username}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 capitalize">
                        {institute.institute_type || 'Unknown'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {institute.city}, {institute.state}
                      </div>
                      <div className="text-sm text-gray-500">
                        {institute.country}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        institute.verification_status === 'approved'
                          ? 'bg-green-100 text-green-800'
                          : institute.verification_status === 'rejected'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {institute.verification_status || 'pending'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(institute.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => openModal(institute, 'view')}
                          className="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-md transition-colors"
                        >
                          <FiEye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => openModal(institute, 'approve')}
                          disabled={approveLoading}
                          className="text-green-600 hover:text-green-900 bg-green-50 hover:bg-green-100 px-3 py-1 rounded-md transition-colors disabled:opacity-50"
                        >
                          <FiCheck className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => openModal(institute, 'reject')}
                          disabled={rejectLoading}
                          className="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 px-3 py-1 rounded-md transition-colors disabled:opacity-50"
                        >
                          <FiX className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

        {/* Pagination and Statistics */}
        <div className="mt-8 flex items-center justify-between border-t border-gray-200 pt-6">
          {/* Statistics */}
          <div className="text-sm text-gray-700">
            Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, pendingTotal)} of {pendingTotal} institutes
          </div>

          {/* Pagination Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
              disabled={!pendingHasPrev || pendingLoading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            <span className="text-sm text-gray-700">
              Page {currentPage + 1}
            </span>

            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={!pendingHasNext || pendingLoading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>

            {/* Page Size Selector */}
            <select
              value={pageSize}
              onChange={(e) => {
                setPageSize(Number(e.target.value));
                setCurrentPage(0); // Reset to first page when changing page size
              }}
              className="ml-4 border border-gray-300 rounded-md text-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={10}>10 per page</option>
              <option value={20}>20 per page</option>
              <option value={50}>50 per page</option>
              <option value={100}>100 per page</option>
            </select>
          </div>
        </div>
        </>
      )}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto" style={{ zIndex: 9999 }}>
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center">
            {/* Background overlay */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
              onClick={closeModal}
            ></div>

            {/* Modal panel */}
            <div className="relative inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full mx-4">
              {/* Debug info */}
              {console.log('Modal render - showModal:', showModal, 'modalType:', modalType, 'selectedInstitute:', selectedInstitute)}

              {/* Show loading state while fetching detailed data */}
              {modalType === 'view' && selectedLoading && (
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-2xl font-bold text-gray-900">Institute Details</h3>
                    <button
                      onClick={closeModal}
                      className="text-gray-400 hover:text-gray-600 focus:outline-none"
                    >
                      <FiX className="h-6 w-6" />
                    </button>
                  </div>
                  <div className="mb-4 p-4 bg-blue-100 rounded flex items-center">
                    <LoadingSpinner />
                    <span className="ml-2">Loading detailed institute information...</span>
                  </div>
                </div>
              )}

              {/* Detailed View Modal Content */}
              {modalType === 'view' && selectedInstitute && !selectedLoading && (
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  {/* Modal Header */}
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-2xl font-bold text-gray-900">Institute Details</h3>
                    <button
                      onClick={closeModal}
                      className="text-gray-400 hover:text-gray-600 focus:outline-none"
                    >
                      <FiX className="h-6 w-6" />
                    </button>
                  </div>
                  {/* Institute Header */}
                  <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white mb-6">
                    <div className="flex items-center space-x-4">
                      {(selectedInstitute.profile?.logo_url || selectedInstitute.user?.profile_picture) ? (
                        <img
                          src={selectedInstitute.profile?.logo_url || selectedInstitute.user?.profile_picture}
                          alt={selectedInstitute.profile?.institute_name || selectedInstitute.institute_name}
                          className="w-20 h-20 rounded-lg bg-white/20 object-cover border-2 border-white/30"
                        />
                      ) : (
                        <div className="w-20 h-20 rounded-lg bg-white/20 flex items-center justify-center border-2 border-white/30">
                          <FiHome className="h-10 w-10 text-white" />
                        </div>
                      )}
                      <div>
                        <h2 className="text-2xl font-bold">
                          {selectedInstitute.profile?.institute_name || selectedInstitute.institute_name || 'Unnamed Institute'}
                        </h2>
                        <p className="text-blue-100 text-lg">
                          {selectedInstitute.profile?.institute_type || selectedInstitute.institute_type || 'Unknown Type'}
                        </p>
                        <div className="flex items-center mt-2 text-blue-100">
                          <FiMapPin className="h-4 w-4 mr-2" />
                          {selectedInstitute.profile?.city || selectedInstitute.city}, {selectedInstitute.profile?.state || selectedInstitute.state}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Content Grid */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <FiHome className="h-5 w-5 mr-2 text-blue-600" />
                        Basic Information
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-gray-500">Institute Name</label>
                          <p className="text-gray-900">{selectedInstitute.profile?.institute_name || selectedInstitute.institute_name || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Type</label>
                          <p className="text-gray-900 capitalize">{selectedInstitute.profile?.institute_type || selectedInstitute.institute_type || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Username</label>
                          <p className="text-gray-900">{selectedInstitute.user?.username || selectedInstitute.username || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Verification Status</label>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            (selectedInstitute.verification_status || selectedInstitute.profile?.verification_status) === 'approved' ? 'bg-green-100 text-green-800' :
                            (selectedInstitute.verification_status || selectedInstitute.profile?.verification_status) === 'rejected' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {selectedInstitute.verification_status || selectedInstitute.profile?.verification_status || 'pending'}
                          </span>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Is Verified</label>
                          <p className="text-gray-900">{(selectedInstitute.profile?.is_verified || selectedInstitute.is_verified) ? 'Yes' : 'No'}</p>
                        </div>
                        {selectedInstitute.profile?.established_year && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Established Year</label>
                            <p className="text-gray-900">{selectedInstitute.profile.established_year}</p>
                          </div>
                        )}
                        {selectedInstitute.profile?.accreditation && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Accreditation</label>
                            <p className="text-gray-900">{selectedInstitute.profile.accreditation}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Contact Information */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <FiMail className="h-5 w-5 mr-2 text-blue-600" />
                        Contact Information
                      </h4>
                      <div className="space-y-3">
                        {selectedInstitute.user?.email && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Email</label>
                            <p className="text-gray-900">{selectedInstitute.user.email}</p>
                          </div>
                        )}
                        {selectedInstitute.user?.mobile && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Mobile</label>
                            <p className="text-gray-900">{selectedInstitute.user.mobile}</p>
                          </div>
                        )}
                        <div>
                          <label className="text-sm font-medium text-gray-500">Email Verified</label>
                          <p className="text-gray-900">{selectedInstitute.user?.is_email_verified ? 'Yes' : 'No'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Mobile Verified</label>
                          <p className="text-gray-900">{selectedInstitute.user?.is_mobile_verified ? 'Yes' : 'No'}</p>
                        </div>
                        {selectedInstitute.profile?.website && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Website</label>
                            <a
                              href={selectedInstitute.profile.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800"
                            >
                              {selectedInstitute.profile.website}
                            </a>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Location Information */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <FiMapPin className="h-5 w-5 mr-2 text-blue-600" />
                        Location Information
                      </h4>
                      <div className="space-y-3">
                        {selectedInstitute.profile?.address && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Address</label>
                            <p className="text-gray-900">{selectedInstitute.profile.address}</p>
                          </div>
                        )}
                        <div>
                          <label className="text-sm font-medium text-gray-500">City</label>
                          <p className="text-gray-900">{selectedInstitute.profile?.city || selectedInstitute.city || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">State</label>
                          <p className="text-gray-900">{selectedInstitute.profile?.state || selectedInstitute.state || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Country</label>
                          <p className="text-gray-900">{selectedInstitute.user?.country || selectedInstitute.country || 'N/A'}</p>
                        </div>
                        {selectedInstitute.profile?.postal_code && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Postal Code</label>
                            <p className="text-gray-900">{selectedInstitute.profile.postal_code}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Statistics */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <FiUsers className="h-5 w-5 mr-2 text-blue-600" />
                        Statistics
                      </h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-white rounded-lg">
                          <div className="text-2xl font-bold text-blue-600">
                            {selectedInstitute.total_competitions || 0}
                          </div>
                          <div className="text-sm text-gray-500">Total Competitions</div>
                        </div>
                        <div className="text-center p-3 bg-white rounded-lg">
                          <div className="text-2xl font-bold text-green-600">
                            {selectedInstitute.total_mentors || 0}
                          </div>
                          <div className="text-sm text-gray-500">Total Mentors</div>
                        </div>
                        <div className="text-center p-3 bg-white rounded-lg">
                          <div className="text-2xl font-bold text-purple-600">
                            {selectedInstitute.active_competitions || 0}
                          </div>
                          <div className="text-sm text-gray-500">Active Competitions</div>
                        </div>
                        <div className="text-center p-3 bg-white rounded-lg">
                          <div className="text-2xl font-bold text-gray-600">
                            {selectedInstitute.user?.user_type || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500">User Type</div>
                        </div>
                      </div>
                    </div>

                    {/* Social Media Links */}
                    {(selectedInstitute.profile?.linkedin_url || selectedInstitute.profile?.facebook_url || selectedInstitute.profile?.twitter_url) && (
                      <div className="bg-gray-50 rounded-lg p-6">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <FiGlobe className="h-5 w-5 mr-2 text-blue-600" />
                          Social Media
                        </h4>
                        <div className="space-y-3">
                          {selectedInstitute.profile?.linkedin_url && (
                            <div>
                              <label className="text-sm font-medium text-gray-500">LinkedIn</label>
                              <a
                                href={selectedInstitute.profile.linkedin_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 block"
                              >
                                {selectedInstitute.profile.linkedin_url}
                              </a>
                            </div>
                          )}
                          {selectedInstitute.profile?.facebook_url && (
                            <div>
                              <label className="text-sm font-medium text-gray-500">Facebook</label>
                              <a
                                href={selectedInstitute.profile.facebook_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 block"
                              >
                                {selectedInstitute.profile.facebook_url}
                              </a>
                            </div>
                          )}
                          {selectedInstitute.profile?.twitter_url && (
                            <div>
                              <label className="text-sm font-medium text-gray-500">Twitter</label>
                              <a
                                href={selectedInstitute.profile.twitter_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 block"
                              >
                                {selectedInstitute.profile.twitter_url}
                              </a>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Description */}
                    {selectedInstitute.profile?.description && (
                      <div className="bg-gray-50 rounded-lg p-6 lg:col-span-2">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <FiFileText className="h-5 w-5 mr-2 text-blue-600" />
                          Description
                        </h4>
                        <p className="text-gray-700 leading-relaxed">{selectedInstitute.profile.description}</p>
                      </div>
                    )}

                    {/* Verification Notes */}
                    {selectedInstitute.profile?.verification_notes && (
                      <div className="bg-yellow-50 rounded-lg p-6 lg:col-span-2">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <FiAlertCircle className="h-5 w-5 mr-2 text-yellow-600" />
                          Verification Notes
                        </h4>
                        <p className="text-gray-700 leading-relaxed">{selectedInstitute.profile.verification_notes}</p>
                      </div>
                    )}

                    {/* Timestamps */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <FiCalendar className="h-5 w-5 mr-2 text-blue-600" />
                        Timestamps
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-gray-500">User Created</label>
                          <p className="text-gray-900">{new Date(selectedInstitute.user?.created_at || selectedInstitute.created_at).toLocaleString()}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Profile Created</label>
                          <p className="text-gray-900">{new Date(selectedInstitute.profile?.created_at || selectedInstitute.created_at).toLocaleString()}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Profile Updated</label>
                          <p className="text-gray-900">{new Date(selectedInstitute.profile?.updated_at || selectedInstitute.updated_at).toLocaleString()}</p>
                        </div>
                        {selectedInstitute.profile?.verified_at && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Verified At</label>
                            <p className="text-gray-900">{new Date(selectedInstitute.profile.verified_at).toLocaleString()}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* System Information */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <FiFileText className="h-5 w-5 mr-2 text-blue-600" />
                        System Information
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-gray-500">User ID</label>
                          <p className="text-gray-900 font-mono text-sm">{selectedInstitute.user?.id}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Profile ID</label>
                          <p className="text-gray-900 font-mono text-sm">{selectedInstitute.profile?.id}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Modal Footer */}
                  <div className="mt-6 flex justify-end space-x-3 border-t border-gray-200 pt-4">
                    <button
                      onClick={closeModal}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Close
                    </button>
                    <button
                      onClick={() => setModalType('approve')}
                      className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                      <FiCheck className="h-4 w-4 mr-2 inline" />
                      Approve Institute
                    </button>
                    <button
                      onClick={() => setModalType('reject')}
                      className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <FiX className="h-4 w-4 mr-2 inline" />
                      Reject Institute
                    </button>
                  </div>
                </div>
              )}

                  {/* Modal Footer */}
                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      onClick={closeModal}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Close
                    </button>
                    <button
                      onClick={() => setModalType('approve')}
                      className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                      Approve Institute
                    </button>
                    <button
                      onClick={() => setModalType('reject')}
                      className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      Reject Institute
                    </button>
                  </div>
                </div>
              )}

              {/* Approve Modal */}
              {modalType === 'approve' && selectedInstitute && (
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-900">Approve Institute</h3>
                    <button
                      onClick={closeModal}
                      className="text-gray-400 hover:text-gray-600 focus:outline-none"
                    >
                      <FiX className="h-6 w-6" />
                    </button>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
                      <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                        <FiHome className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">
                          {selectedInstitute.institute_name || 'Unnamed Institute'}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {selectedInstitute.institute_type || 'Unknown Type'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Approval Notes (Optional)
                    </label>
                    <textarea
                      value={approvalNotes}
                      onChange={(e) => setApprovalNotes(e.target.value)}
                      rows={4}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Add any notes about the approval..."
                    />
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={closeModal}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleApprove}
                      disabled={approveLoading}
                      className="px-6 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {approveLoading ? (
                        <>
                          <LoadingSpinner />
                          Approving...
                        </>
                      ) : (
                        <>
                          <FiCheck className="h-4 w-4 mr-2 inline" />
                          Approve Institute
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Reject Modal */}
              {modalType === 'reject' && selectedInstitute && (
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-900">Reject Institute</h3>
                    <button
                      onClick={closeModal}
                      className="text-gray-400 hover:text-gray-600 focus:outline-none"
                    >
                      <FiX className="h-6 w-6" />
                    </button>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center space-x-4 p-4 bg-red-50 rounded-lg">
                      <div className="w-12 h-12 rounded-lg bg-red-100 flex items-center justify-center">
                        <FiHome className="h-6 w-6 text-red-600" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">
                          {selectedInstitute.institute_name || 'Unnamed Institute'}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {selectedInstitute.institute_type || 'Unknown Type'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Rejection Reason <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      rows={4}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      placeholder="Please provide a reason for rejection..."
                      required
                    />
                    {!rejectionReason.trim() && (
                      <p className="mt-1 text-sm text-red-600">Rejection reason is required</p>
                    )}
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={closeModal}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleReject}
                      disabled={rejectLoading || !rejectionReason.trim()}
                      className="px-6 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {rejectLoading ? (
                        <>
                          <LoadingSpinner />
                          Rejecting...
                        </>
                      ) : (
                        <>
                          <FiX className="h-4 w-4 mr-2 inline" />
                          Reject Institute
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstituteApprovals;
