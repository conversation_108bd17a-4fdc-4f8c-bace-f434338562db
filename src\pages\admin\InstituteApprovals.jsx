import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiHome,
  FiMail,
  FiPhone,
  FiMapPin,
  FiGlobe,
  FiCheck,
  FiX,
  FiEye,
  FiClock,
  FiRefreshCw,
  FiAlertCircle,
  FiUsers,
  FiCalendar,
  FiFileText
} from 'react-icons/fi';
import {
  fetchPendingVerificationInstitutes,
  fetchPendingInstitutes, // Keep for backward compatibility
  fetchInstituteDetailsAdmin,
  approveInstituteProfile,
  rejectInstituteProfile,
  selectPendingInstitutes,
  selectPendingLoading,
  selectPendingError,
  selectPendingTotal,
  selectPendingPage,
  selectPendingSize,
  selectPendingHasNext,
  selectPendingHasPrev,
  selectApproveLoading,
  selectApproveSuccess,
  selectRejectLoading,
  selectRejectSuccess,
  clearErrors,
  clearSuccessStates
} from '../../store/slices/InstituteProfileSlice';
import { LoadingSpinner } from '../../components/ui';

const InstituteApprovals = () => {
  const dispatch = useDispatch();
  
  // Redux state
  const pendingInstitutesRaw = useSelector(selectPendingInstitutes);
  const pendingInstitutes = Array.isArray(pendingInstitutesRaw) ? pendingInstitutesRaw : [];
  const pendingLoading = useSelector(selectPendingLoading);
  const pendingError = useSelector(selectPendingError);
  const pendingTotal = useSelector(selectPendingTotal);
  const pendingPage = useSelector(selectPendingPage);
  const pendingSize = useSelector(selectPendingSize);
  const pendingHasNext = useSelector(selectPendingHasNext);
  const pendingHasPrev = useSelector(selectPendingHasPrev);
  const approveLoading = useSelector(selectApproveLoading);
  const approveSuccess = useSelector(selectApproveSuccess);
  const rejectLoading = useSelector(selectRejectLoading);
  const rejectSuccess = useSelector(selectRejectSuccess);

  // Local state
  const [selectedInstitute, setSelectedInstitute] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(''); // 'approve', 'reject', 'view'
  const [rejectionReason, setRejectionReason] = useState('');
  const [approvalNotes, setApprovalNotes] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(20);

  // Load pending institutes on component mount and when pagination changes
  useEffect(() => {
    dispatch(fetchPendingVerificationInstitutes({
      skip: currentPage * pageSize,
      limit: pageSize
    }));
  }, [dispatch, currentPage, pageSize]);

  // Handle success states
  useEffect(() => {
    if (approveSuccess || rejectSuccess) {
      setShowModal(false);
      setSelectedInstitute(null);
      setRejectionReason('');
      setApprovalNotes('');
      dispatch(fetchPendingVerificationInstitutes({
        skip: currentPage * pageSize,
        limit: pageSize
      })); // Refresh the list
      
      const timer = setTimeout(() => {
        dispatch(clearSuccessStates());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [approveSuccess, rejectSuccess, dispatch]);

  // Handle approve
  const handleApprove = async () => {
    if (!selectedInstitute) return;
    
    try {
      await dispatch(approveInstituteProfile({
        instituteId: selectedInstitute.id,
        approvalData: {
          notes: approvalNotes,
          approved_at: new Date().toISOString()
        }
      })).unwrap();
    } catch (error) {
      console.error('Failed to approve institute:', error);
    }
  };

  // Handle reject
  const handleReject = async () => {
    if (!selectedInstitute || !rejectionReason.trim()) {
      alert('Please provide a reason for rejection.');
      return;
    }
    
    try {
      await dispatch(rejectInstituteProfile({
        instituteId: selectedInstitute.id,
        rejectionData: {
          reason: rejectionReason,
          rejected_at: new Date().toISOString()
        }
      })).unwrap();
    } catch (error) {
      console.error('Failed to reject institute:', error);
    }
  };

  // Fetch detailed institute information
  const fetchInstituteDetails = async (instituteId) => {
    try {
      const details = await dispatch(fetchInstituteDetailsAdmin(instituteId)).unwrap();
      return details;
    } catch (error) {
      console.error('Failed to fetch institute details:', error);
      return null;
    }
  };

  // Open modal
  const openModal = async (institute, type) => {
    setSelectedInstitute(institute);
    setModalType(type);
    setShowModal(true);

    // If viewing details, fetch complete institute information
    if (type === 'view') {
      const details = await fetchInstituteDetails(institute.id);
      if (details) {
        setSelectedInstitute(details);
      }
    }
  };

  // Close modal
  const closeModal = () => {
    setShowModal(false);
    setSelectedInstitute(null);
    setModalType('');
    setRejectionReason('');
    setApprovalNotes('');
  };

  if (pendingLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Institute Approvals</h1>
            <p className="text-gray-600 mt-2">
              Review and approve institute profile submissions
            </p>
          </div>
          <button
            onClick={() => dispatch(fetchPendingVerificationInstitutes({
              skip: currentPage * pageSize,
              limit: pageSize
            }))}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiRefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Success Messages */}
      {(approveSuccess || rejectSuccess) && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <FiCheck className="h-5 w-5 text-green-600 mr-3" />
            <p className="text-green-700">
              {approveSuccess && 'Institute approved successfully!'}
              {rejectSuccess && 'Institute rejected successfully!'}
            </p>
          </div>
        </div>
      )}

      {/* Error Message */}
      {pendingError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <FiAlertCircle className="h-5 w-5 text-red-600 mr-3" />
            <p className="text-red-700">{pendingError}</p>
          </div>
        </div>
      )}

      {/* Pending Institutes List */}
      {pendingInstitutes.length === 0 ? (
        <div className="text-center py-12">
          <FiHome className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No pending approvals</h3>
          <p className="mt-1 text-sm text-gray-500">
            All institute profiles have been reviewed.
          </p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {pendingInstitutes.map((institute) => (
            <div key={institute.id} className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              {/* Institute Header with Logo/Banner */}
              <div className="relative bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-4">
                    {institute.profile_picture || institute.logo_url ? (
                      <img
                        src={institute.profile_picture || institute.logo_url}
                        alt={institute.institute_name}
                        className="w-16 h-16 rounded-lg bg-white/20 object-cover border-2 border-white/30"
                      />
                    ) : (
                      <div className="w-16 h-16 rounded-lg bg-white/20 flex items-center justify-center border-2 border-white/30">
                        <FiHome className="h-8 w-8 text-white" />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-xl font-bold text-white truncate">
                        {institute.institute_name || 'Unnamed Institute'}
                      </h3>
                      <p className="text-blue-100 text-sm capitalize">
                        {institute.institute_type || 'Unknown Type'}
                      </p>
                      <div className="flex items-center mt-1 text-blue-100 text-xs">
                        <FiMapPin className="h-3 w-3 mr-1" />
                        {institute.city}, {institute.state}
                      </div>
                    </div>
                  </div>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-yellow-400 text-yellow-900 shadow-sm">
                    <FiClock className="h-3 w-3 mr-1" />
                    {institute.verification_status || 'Pending'}
                  </span>
                </div>
              </div>

              {/* Institute Details */}
              <div className="p-6">
                {/* Quick Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-gray-900">
                      {new Date(institute.created_at).getFullYear()}
                    </div>
                    <div className="text-xs text-gray-500">Joined</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-gray-900">
                      {institute.is_verified ? 'Yes' : 'No'}
                    </div>
                    <div className="text-xs text-gray-500">Verified</div>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <FiUsers className="h-4 w-4 mr-3 text-gray-400" />
                    <span className="font-medium">Username:</span>
                    <span className="ml-2">{institute.username}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <FiGlobe className="h-4 w-4 mr-3 text-gray-400" />
                    <span className="font-medium">Country:</span>
                    <span className="ml-2">{institute.country}</span>
                  </div>
                </div>

                {/* Timestamps */}
                <div className="text-xs text-gray-500 mb-4 p-3 bg-gray-50 rounded-lg">
                  <div className="flex justify-between">
                    <span>Created: {new Date(institute.created_at).toLocaleDateString()}</span>
                    <span>Updated: {new Date(institute.updated_at).toLocaleDateString()}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => openModal(institute, 'view')}
                    className="flex-1 inline-flex items-center justify-center px-4 py-2.5 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                  >
                    <FiEye className="h-4 w-4 mr-2" />
                    View Details
                  </button>
                  <button
                    onClick={() => openModal(institute, 'approve')}
                    disabled={approveLoading}
                    className="flex-1 inline-flex items-center justify-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  >
                    <FiCheck className="h-4 w-4 mr-2" />
                    Approve
                  </button>
                  <button
                    onClick={() => openModal(institute, 'reject')}
                    disabled={rejectLoading}
                    className="flex-1 inline-flex items-center justify-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  >
                    <FiX className="h-4 w-4 mr-2" />
                    Reject
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination and Statistics */}
        <div className="mt-8 flex items-center justify-between border-t border-gray-200 pt-6">
          {/* Statistics */}
          <div className="text-sm text-gray-700">
            Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, pendingTotal)} of {pendingTotal} institutes
          </div>

          {/* Pagination Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
              disabled={!pendingHasPrev || pendingLoading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            <span className="text-sm text-gray-700">
              Page {currentPage + 1}
            </span>

            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={!pendingHasNext || pendingLoading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>

            {/* Page Size Selector */}
            <select
              value={pageSize}
              onChange={(e) => {
                setPageSize(Number(e.target.value));
                setCurrentPage(0); // Reset to first page when changing page size
              }}
              className="ml-4 border border-gray-300 rounded-md text-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={10}>10 per page</option>
              <option value={20}>20 per page</option>
              <option value={50}>50 per page</option>
              <option value={100}>100 per page</option>
            </select>
          </div>
        </div>
        </>
      )}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
              onClick={closeModal}
            ></div>

            {/* Modal panel */}
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              {modalType === 'view' && selectedInstitute && (
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  {/* Modal Header */}
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-2xl font-bold text-gray-900">Institute Details</h3>
                    <button
                      onClick={closeModal}
                      className="text-gray-400 hover:text-gray-600 focus:outline-none"
                    >
                      <FiX className="h-6 w-6" />
                    </button>
                  </div>

                  {/* Institute Header */}
                  <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white mb-6">
                    <div className="flex items-center space-x-4">
                      {selectedInstitute.profile?.logo_url ? (
                        <img
                          src={selectedInstitute.profile.logo_url}
                          alt={selectedInstitute.profile?.institute_name}
                          className="w-20 h-20 rounded-lg bg-white/20 object-cover border-2 border-white/30"
                        />
                      ) : (
                        <div className="w-20 h-20 rounded-lg bg-white/20 flex items-center justify-center border-2 border-white/30">
                          <FiHome className="h-10 w-10 text-white" />
                        </div>
                      )}
                      <div>
                        <h2 className="text-2xl font-bold">
                          {selectedInstitute.profile?.institute_name || selectedInstitute.institute_name || 'Unnamed Institute'}
                        </h2>
                        <p className="text-blue-100 text-lg">
                          {selectedInstitute.profile?.institute_type || selectedInstitute.institute_type || 'Unknown Type'}
                        </p>
                        <div className="flex items-center mt-2 text-blue-100">
                          <FiMapPin className="h-4 w-4 mr-2" />
                          {selectedInstitute.profile?.city || selectedInstitute.city}, {selectedInstitute.profile?.state || selectedInstitute.state}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Content Grid */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <FiHome className="h-5 w-5 mr-2 text-blue-600" />
                        Basic Information
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-gray-500">Institute Name</label>
                          <p className="text-gray-900">{selectedInstitute.profile?.institute_name || selectedInstitute.institute_name || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Type</label>
                          <p className="text-gray-900 capitalize">{selectedInstitute.profile?.institute_type || selectedInstitute.institute_type || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Username</label>
                          <p className="text-gray-900">{selectedInstitute.user?.username || selectedInstitute.username || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Verification Status</label>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            selectedInstitute.verification_status === 'approved' ? 'bg-green-100 text-green-800' :
                            selectedInstitute.verification_status === 'rejected' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {selectedInstitute.verification_status || 'pending'}
                          </span>
                        </div>
                        {selectedInstitute.profile?.established_year && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Established Year</label>
                            <p className="text-gray-900">{selectedInstitute.profile.established_year}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Contact Information */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <FiMail className="h-5 w-5 mr-2 text-blue-600" />
                        Contact Information
                      </h4>
                      <div className="space-y-3">
                        {selectedInstitute.user?.email && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Email</label>
                            <p className="text-gray-900">{selectedInstitute.user.email}</p>
                          </div>
                        )}
                        {selectedInstitute.user?.mobile && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Mobile</label>
                            <p className="text-gray-900">{selectedInstitute.user.mobile}</p>
                          </div>
                        )}
                        {selectedInstitute.profile?.address && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Address</label>
                            <p className="text-gray-900">{selectedInstitute.profile.address}</p>
                          </div>
                        )}
                        <div>
                          <label className="text-sm font-medium text-gray-500">City</label>
                          <p className="text-gray-900">{selectedInstitute.profile?.city || selectedInstitute.city || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">State</label>
                          <p className="text-gray-900">{selectedInstitute.profile?.state || selectedInstitute.state || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Country</label>
                          <p className="text-gray-900">{selectedInstitute.user?.country || selectedInstitute.country || 'N/A'}</p>
                        </div>
                        {selectedInstitute.profile?.website && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Website</label>
                            <a
                              href={selectedInstitute.profile.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800"
                            >
                              {selectedInstitute.profile.website}
                            </a>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Statistics */}
                    {(selectedInstitute.total_competitions !== undefined || selectedInstitute.total_mentors !== undefined) && (
                      <div className="bg-gray-50 rounded-lg p-6">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <FiUsers className="h-5 w-5 mr-2 text-blue-600" />
                          Statistics
                        </h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center p-3 bg-white rounded-lg">
                            <div className="text-2xl font-bold text-blue-600">
                              {selectedInstitute.total_competitions || 0}
                            </div>
                            <div className="text-sm text-gray-500">Total Competitions</div>
                          </div>
                          <div className="text-center p-3 bg-white rounded-lg">
                            <div className="text-2xl font-bold text-green-600">
                              {selectedInstitute.total_mentors || 0}
                            </div>
                            <div className="text-sm text-gray-500">Total Mentors</div>
                          </div>
                          <div className="text-center p-3 bg-white rounded-lg">
                            <div className="text-2xl font-bold text-purple-600">
                              {selectedInstitute.active_competitions || 0}
                            </div>
                            <div className="text-sm text-gray-500">Active Competitions</div>
                          </div>
                          <div className="text-center p-3 bg-white rounded-lg">
                            <div className="text-2xl font-bold text-gray-600">
                              {selectedInstitute.user?.is_email_verified ? 'Yes' : 'No'}
                            </div>
                            <div className="text-sm text-gray-500">Email Verified</div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Description */}
                    {selectedInstitute.profile?.description && (
                      <div className="bg-gray-50 rounded-lg p-6">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <FiFileText className="h-5 w-5 mr-2 text-blue-600" />
                          Description
                        </h4>
                        <p className="text-gray-700 leading-relaxed">{selectedInstitute.profile.description}</p>
                      </div>
                    )}
                  </div>

                  {/* Modal Footer */}
                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      onClick={closeModal}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Close
                    </button>
                    <button
                      onClick={() => setModalType('approve')}
                      className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                      Approve Institute
                    </button>
                    <button
                      onClick={() => setModalType('reject')}
                      className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      Reject Institute
                    </button>
                  </div>
                </div>
              )}

              {/* Approve Modal */}
              {modalType === 'approve' && selectedInstitute && (
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-900">Approve Institute</h3>
                    <button
                      onClick={closeModal}
                      className="text-gray-400 hover:text-gray-600 focus:outline-none"
                    >
                      <FiX className="h-6 w-6" />
                    </button>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
                      <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                        <FiHome className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">
                          {selectedInstitute.institute_name || 'Unnamed Institute'}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {selectedInstitute.institute_type || 'Unknown Type'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Approval Notes (Optional)
                    </label>
                    <textarea
                      value={approvalNotes}
                      onChange={(e) => setApprovalNotes(e.target.value)}
                      rows={4}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Add any notes about the approval..."
                    />
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={closeModal}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleApprove}
                      disabled={approveLoading}
                      className="px-6 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {approveLoading ? (
                        <>
                          <LoadingSpinner />
                          Approving...
                        </>
                      ) : (
                        <>
                          <FiCheck className="h-4 w-4 mr-2 inline" />
                          Approve Institute
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Reject Modal */}
              {modalType === 'reject' && selectedInstitute && (
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-900">Reject Institute</h3>
                    <button
                      onClick={closeModal}
                      className="text-gray-400 hover:text-gray-600 focus:outline-none"
                    >
                      <FiX className="h-6 w-6" />
                    </button>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center space-x-4 p-4 bg-red-50 rounded-lg">
                      <div className="w-12 h-12 rounded-lg bg-red-100 flex items-center justify-center">
                        <FiHome className="h-6 w-6 text-red-600" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">
                          {selectedInstitute.institute_name || 'Unnamed Institute'}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {selectedInstitute.institute_type || 'Unknown Type'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Rejection Reason <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      rows={4}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      placeholder="Please provide a reason for rejection..."
                      required
                    />
                    {!rejectionReason.trim() && (
                      <p className="mt-1 text-sm text-red-600">Rejection reason is required</p>
                    )}
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={closeModal}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleReject}
                      disabled={rejectLoading || !rejectionReason.trim()}
                      className="px-6 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {rejectLoading ? (
                        <>
                          <LoadingSpinner />
                          Rejecting...
                        </>
                      ) : (
                        <>
                          <FiX className="h-4 w-4 mr-2 inline" />
                          Reject Institute
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstituteApprovals;
