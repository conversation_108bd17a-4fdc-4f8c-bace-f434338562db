import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiSearch,
  FiFilter,
  FiMapPin,
  FiUsers,
  FiCalendar,
  FiEye,
  FiMail,
  FiPhone,
  FiGlobe,
  FiAward,
  FiRefreshCw
} from 'react-icons/fi';
import SearchFilterCard from '../../components/ui/SearchFilterCard';
import { LoadingSpinner } from '../../components/ui';

function SponsorInstitutes() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterLocation, setFilterLocation] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [loading, setLoading] = useState(false);

  // Mock institutes data - in real app, this would come from an API
  const [institutes] = useState([
    {
      id: 1,
      name: 'Springfield University',
      type: 'University',
      location: 'Springfield, IL',
      country: 'USA',
      students_count: 15000,
      established: 1965,
      status: 'active',
      partnership_status: 'active',
      contact_email: '<EMAIL>',
      contact_phone: '******-0123',
      website: 'https://springfield.edu',
      logo_url: null,
      description: 'A leading public university known for excellence in research and education.',
      specializations: ['Engineering', 'Business', 'Medicine', 'Arts']
    },
    {
      id: 2,
      name: 'Riverside College',
      type: 'College',
      location: 'Riverside, CA',
      country: 'USA',
      students_count: 8500,
      established: 1978,
      status: 'active',
      partnership_status: 'pending',
      contact_email: '<EMAIL>',
      contact_phone: '******-0456',
      website: 'https://riverside.edu',
      logo_url: null,
      description: 'A private college focused on liberal arts and sciences.',
      specializations: ['Liberal Arts', 'Sciences', 'Psychology', 'Education']
    },
    {
      id: 3,
      name: 'Mountain View Institute',
      type: 'Institute',
      location: 'Mountain View, CO',
      country: 'USA',
      students_count: 3200,
      established: 1995,
      status: 'active',
      partnership_status: 'active',
      contact_email: '<EMAIL>',
      contact_phone: '******-0789',
      website: 'https://mountainview.edu',
      logo_url: null,
      description: 'Specialized institute for technology and innovation.',
      specializations: ['Technology', 'Innovation', 'Computer Science', 'AI']
    },
    {
      id: 4,
      name: 'Coastal Academy',
      type: 'Academy',
      location: 'Miami, FL',
      country: 'USA',
      students_count: 5600,
      established: 1987,
      status: 'active',
      partnership_status: 'inactive',
      contact_email: '<EMAIL>',
      contact_phone: '******-0321',
      website: 'https://coastal.edu',
      logo_url: null,
      description: 'Maritime and coastal studies academy.',
      specializations: ['Marine Biology', 'Oceanography', 'Environmental Science', 'Maritime Studies']
    }
  ]);

  // Search and filter handlers
  const handleSearchChange = (value) => {
    setSearchTerm(value);
  };

  const handleSearchSubmit = (value) => {
    setSearchTerm(value);
  };

  const handleSearchClear = () => {
    setSearchTerm('');
  };

  const handleRefresh = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  // Filter institutes based on search and filters
  const filteredInstitutes = institutes.filter(institute => {
    const matchesSearch = !searchTerm ||
      institute.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      institute.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      institute.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      institute.specializations.some(spec => spec.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesType = filterType === 'all' || institute.type.toLowerCase() === filterType.toLowerCase();
    const matchesLocation = filterLocation === 'all' || institute.country.toLowerCase() === filterLocation.toLowerCase();
    const matchesStatus = filterStatus === 'all' || institute.partnership_status === filterStatus;

    return matchesSearch && matchesType && matchesLocation && matchesStatus;
  });

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { bg: 'bg-green-100', text: 'text-green-800', label: 'Active' },
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      inactive: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Inactive' }
    };

    const config = statusConfig[status] || statusConfig.inactive;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            Partner Institutes
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your educational institute partnerships and collaborations
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <FiRefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {/* Search and Filters */}
      <SearchFilterCard
        searchValue={searchTerm}
        onSearchChange={handleSearchChange}
        onSearchSubmit={handleSearchSubmit}
        onSearchClear={handleSearchClear}
        searchPlaceholder="Search institutes by name, location, or specialization..."
        filters={[
          {
            label: 'Type',
            value: filterType,
            onChange: setFilterType,
            options: [
              { value: 'all', label: 'All Types' },
              { value: 'university', label: 'University' },
              { value: 'college', label: 'College' },
              { value: 'institute', label: 'Institute' },
              { value: 'academy', label: 'Academy' }
            ]
          },
          {
            label: 'Location',
            value: filterLocation,
            onChange: setFilterLocation,
            options: [
              { value: 'all', label: 'All Locations' },
              { value: 'usa', label: 'USA' },
              { value: 'canada', label: 'Canada' },
              { value: 'uk', label: 'United Kingdom' },
              { value: 'australia', label: 'Australia' }
            ]
          },
          {
            label: 'Partnership Status',
            value: filterStatus,
            onChange: setFilterStatus,
            options: [
              { value: 'all', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'pending', label: 'Pending' },
              { value: 'inactive', label: 'Inactive' }
            ]
          }
        ]}
        resultsCount={filteredInstitutes.length}
        resultsType="institutes"
        showViewToggle={false}
      />

      {/* Institutes Grid */}
      {loading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : filteredInstitutes.length === 0 ? (
        <div className="text-center py-12">
          <FiUsers className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            {searchTerm || filterType !== 'all' || filterLocation !== 'all' || filterStatus !== 'all'
              ? 'No institutes match your criteria'
              : 'No partner institutes'
            }
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || filterType !== 'all' || filterLocation !== 'all' || filterStatus !== 'all'
              ? 'Try adjusting your search or filters.'
              : 'Partner institutes will appear here once partnerships are established.'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredInstitutes.map((institute) => (
            <div key={institute.id} className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
              {/* Institute Header */}
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {institute.logo_url ? (
                        <img
                          src={institute.logo_url}
                          alt={institute.name}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                          <FiUsers className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
                        {institute.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {institute.type}
                      </p>
                    </div>
                  </div>
                  {getStatusBadge(institute.partnership_status)}
                </div>

                <p className="mt-3 text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                  {institute.description}
                </p>

                {/* Institute Details */}
                <div className="mt-4 space-y-2">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <FiMapPin className="h-4 w-4 mr-2" />
                    {institute.location}
                  </div>
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <FiUsers className="h-4 w-4 mr-2" />
                    {institute.students_count.toLocaleString()} students
                  </div>
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <FiCalendar className="h-4 w-4 mr-2" />
                    Established {institute.established}
                  </div>
                </div>

                {/* Specializations */}
                <div className="mt-4">
                  <div className="flex flex-wrap gap-1">
                    {institute.specializations.slice(0, 3).map((spec, index) => (
                      <span
                        key={index}
                        className="inline-block px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded"
                      >
                        {spec}
                      </span>
                    ))}
                    {institute.specializations.length > 3 && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        +{institute.specializations.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Contact Info */}
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-3">
                      <a
                        href={`mailto:${institute.contact_email}`}
                        className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                        title="Email"
                      >
                        <FiMail className="h-4 w-4" />
                      </a>
                      <a
                        href={`tel:${institute.contact_phone}`}
                        className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                        title="Phone"
                      >
                        <FiPhone className="h-4 w-4" />
                      </a>
                      <a
                        href={institute.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                        title="Website"
                      >
                        <FiGlobe className="h-4 w-4" />
                      </a>
                    </div>
                    <button
                      onClick={() => console.log('View institute details:', institute)}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 shadow-sm text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <FiEye className="h-3 w-3 mr-1" />
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default SponsorInstitutes;