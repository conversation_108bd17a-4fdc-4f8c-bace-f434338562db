import React from 'react';
import {
  FiSave,
  FiSend,
  FiX,
  FiEdit3,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>lock,
  FiAlertCircle
} from 'react-icons/fi';
import { LoadingSpinner } from '../../ui';

const InstituteProfileActions = ({
  isEditing,
  profileNotFound,
  approvalStatus,
  saveLoading,
  submitLoading,
  saveSuccess,
  submitSuccess,
  isFormValid,
  hasUnsavedChanges,
  onSave,
  onSubmit,
  onToggleEdit,
  onCancelEdit
}) => {
  // Determine if profile can be submitted
  const canSubmit = () => {
    return isFormValid && 
           !profileNotFound && 
           (approvalStatus === 'draft' || approvalStatus === 'rejected');
  };

  // Determine if profile can be saved
  const canSave = () => {
    return isEditing && hasUnsavedChanges;
  };

  // Get submit button configuration
  const getSubmitConfig = () => {
    if (approvalStatus === 'pending') {
      return {
        disabled: true,
        text: 'Submitted for Review',
        icon: Fi<PERSON><PERSON>,
        className: 'bg-yellow-100 text-yellow-800 cursor-not-allowed'
      };
    }
    
    if (approvalStatus === 'approved') {
      return {
        disabled: true,
        text: 'Profile Approved',
        icon: FiCheck,
        className: 'bg-green-100 text-green-800 cursor-not-allowed'
      };
    }

    if (!isFormValid) {
      return {
        disabled: true,
        text: 'Complete Required Fields',
        icon: FiAlertCircle,
        className: 'bg-gray-100 text-gray-500 cursor-not-allowed'
      };
    }

    return {
      disabled: false,
      text: approvalStatus === 'rejected' ? 'Resubmit for Review' : 'Submit for Review',
      icon: FiSend,
      className: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
    };
  };

  const submitConfig = getSubmitConfig();

  if (!isEditing && !profileNotFound) {
    // View mode - show edit button
    return (
      <div className="bg-white rounded-lg shadow border border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Profile Actions</h3>
              <p className="text-sm text-gray-600">
                Make changes to your institute profile
              </p>
            </div>
            <button
              onClick={onToggleEdit}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FiEdit3 className="h-4 w-4 mr-2" />
              Edit Profile
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Actions</h3>
      </div>

      <div className="p-6">
        {/* Success Messages */}
        {saveSuccess && (
          <div className="mb-4 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <FiCheck className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">
                  Profile saved successfully!
                </p>
              </div>
            </div>
          </div>
        )}

        {submitSuccess && (
          <div className="mb-4 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <FiCheck className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">
                  Profile submitted for review successfully!
                </p>
                <p className="text-sm text-green-700 mt-1">
                  You will be notified once the admin reviews your profile.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Save Button */}
          <button
            onClick={onSave}
            disabled={!canSave() || saveLoading}
            className={`flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
              canSave() && !saveLoading
                ? 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                : 'text-gray-500 bg-gray-100 cursor-not-allowed'
            }`}
          >
            {saveLoading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              <>
                <FiSave className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </button>

          {/* Submit Button */}
          {!profileNotFound && (
            <button
              onClick={onSubmit}
              disabled={submitConfig.disabled || submitLoading}
              className={`flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${submitConfig.className}`}
            >
              {submitLoading ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Submitting...
                </>
              ) : (
                <>
                  <submitConfig.icon className="h-4 w-4 mr-2" />
                  {submitConfig.text}
                </>
              )}
            </button>
          )}

          {/* Cancel Button */}
          <button
            onClick={onCancelEdit}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            <FiX className="h-4 w-4 mr-2" />
            Cancel
          </button>
        </div>

        {/* Help Text */}
        <div className="mt-4 text-sm text-gray-600">
          <p>
            <strong>Save Changes:</strong> Save your progress without submitting for review.
          </p>
          {!profileNotFound && (
            <p className="mt-1">
              <strong>Submit for Review:</strong> Submit your completed profile for admin approval.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default InstituteProfileActions;
