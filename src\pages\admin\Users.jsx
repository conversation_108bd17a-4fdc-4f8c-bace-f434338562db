import { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchAllUsers } from '../../store/slices/userSlice';
import SearchFilterCard from '../../components/ui/SearchFilterCard';
import UserList from '../../components/ui/UserList';
import {
  FiUsers,
  FiEye,
  FiEdit,
  FiTrash2,
  FiMail,
  FiPhone,
  FiShield,
  FiRefreshCw,
  FiMoreVertical
} from 'react-icons/fi';

function AdminUsers() {
  const dispatch = useDispatch();
  const { allUsers, loading, error } = useSelector((state) => state.users);

  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterVerification, setFilterVerification] = useState('all');
  const [viewMode, setViewMode] = useState('table'); // grid or table
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);

  useEffect(() => {
    dispatch(fetchAllUsers());
  }, [dispatch]);

  // Search handlers
  const handleSearchChange = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleSearchSubmit = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleSearchClear = () => {
    setSearchTerm('');
    setCurrentPage(1);
  };

  // Filter handlers
  const handleRoleFilter = (role) => {
    setFilterRole(role);
    setCurrentPage(1);
  };

  const handleVerificationFilter = (verification) => {
    setFilterVerification(verification);
    setCurrentPage(1);
  };

  // Memoized filtering for better performance
  const filteredUsers = useMemo(() => {
    if (!allUsers) return [];

    return allUsers.filter(user => {
      const matchesSearch = !searchTerm ||
        user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.user_type?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesRole = filterRole === 'all' || user.user_type === filterRole;

      const matchesVerification = filterVerification === 'all' ||
        (filterVerification === 'verified' && user.is_email_verified) ||
        (filterVerification === 'unverified' && !user.is_email_verified);

      return matchesSearch && matchesRole && matchesVerification;
    });
  }, [allUsers, searchTerm, filterRole, filterVerification]);

  // Pagination
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage);

  // Stats calculations
  const totalUsers = allUsers?.length || 0;
  const activeUsers = allUsers?.filter(user => user.is_email_verified).length || 0;
  const adminUsers = allUsers?.filter(user => user.user_type === 'admin').length || 0;
  const studentUsers = allUsers?.filter(user => user.user_type === 'student').length || 0;

  // User card component
  const UserCard = ({ user }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-all duration-200 p-6 border border-transparent hover:border-violet-200 dark:hover:border-violet-700">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-violet-100 dark:bg-violet-900/30 rounded-full flex items-center justify-center">
            <span className="text-lg font-semibold text-violet-600 dark:text-violet-400">
              {user.username?.charAt(0).toUpperCase() || 'U'}
            </span>
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {user.username || 'N/A'}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
              <FiMail className="w-4 h-4 mr-1" />
              {user.email || 'N/A'}
              {user.is_email_verified && (
                <FiShield className="w-4 h-4 ml-2 text-green-500" title="Email verified" />
              )}
            </p>
            {user.mobile && (
              <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
                <FiPhone className="w-4 h-4 mr-1" />
                {user.mobile}
                {user.is_mobile_verified && (
                  <FiShield className="w-4 h-4 ml-2 text-green-500" title="Mobile verified" />
                )}
              </p>
            )}
          </div>
        </div>
        <div className="flex flex-col items-end space-y-2">
          <span className={`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${user.user_type === 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
              user.user_type === 'teacher' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
              user.user_type === 'student' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
              user.user_type === 'institute' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
              user.user_type === 'mentor' ? 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200' :
              'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
            }
          `}>
            {user.user_type?.charAt(0).toUpperCase() + user.user_type?.slice(1) || 'Unknown'}
          </span>
          <div className="flex items-center space-x-1">
            <button
              className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              title="View User"
            >
              <FiEye className="w-4 h-4" />
            </button>
            <button
              className="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors"
              title="Edit User"
            >
              <FiEdit className="w-4 h-4" />
            </button>
            <button
              className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              title="More Actions"
            >
              <FiMoreVertical className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500 dark:text-gray-400">
            Joined: {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
          </span>
          <div className="flex space-x-2">
            <span className={`
              inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
              ${user.is_email_verified
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }
            `}>
              Email {user.is_email_verified ? 'Verified' : 'Unverified'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-96"></div>
          </div>
          <div className="flex space-x-3 mt-4 sm:mt-0">
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
          </div>
        </div>

        {/* Stats Skeleton */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 mb-2"></div>
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                </div>
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Content Skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center">
          <FiUsers className="mx-auto text-red-400 mb-4" size={48} />
          <h3 className="text-lg font-medium text-red-900 dark:text-red-100 mb-2">
            Failed to Load Users
          </h3>
          <p className="text-red-700 dark:text-red-400 mb-4">
            {typeof error === 'string' ? error : 'We encountered an error while loading user data.'}
          </p>
          <button
            onClick={() => dispatch(fetchAllUsers())}
            className="btn bg-red-600 hover:bg-red-700 text-white"
          >
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            User Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage all users across the platform
          </p>
        </div>
        <div className="flex items-center gap-3 mt-4 sm:mt-0">
          <button
            onClick={() => dispatch(fetchAllUsers())}
            className="btn btn-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 text-gray-600 dark:text-gray-300"
          >
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Users</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{totalUsers}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
              <FiUsers className="text-blue-600 dark:text-blue-400" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Verified Users</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{activeUsers}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center">
              <FiShield className="text-green-600 dark:text-green-400" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Admins</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{adminUsers}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-xl flex items-center justify-center">
              <FiShield className="text-red-600 dark:text-red-400" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Students</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{studentUsers}</p>
            </div>
            <div className="w-12 h-12 bg-violet-100 dark:bg-violet-900/30 rounded-xl flex items-center justify-center">
              <FiUsers className="text-violet-600 dark:text-violet-400" size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <SearchFilterCard
        searchValue={searchTerm}
        onSearchChange={handleSearchChange}
        onSearchSubmit={handleSearchSubmit}
        onSearchClear={handleSearchClear}
        searchPlaceholder="Search users by name, email, or role..."
        filters={[
          {
            label: 'Role',
            value: filterRole,
            onChange: handleRoleFilter,
            options: [
              { value: 'all', label: 'All Roles' },
              { value: 'admin', label: 'Admin' },
              { value: 'teacher', label: 'Teacher' },
              { value: 'student', label: 'Student' },
              { value: 'institute', label: 'Institute' }
            ]
          },
          {
            label: 'Status',
            value: filterVerification,
            onChange: handleVerificationFilter,
            options: [
              { value: 'all', label: 'All Status' },
              { value: 'verified', label: 'Verified' },
              { value: 'unverified', label: 'Unverified' }
            ]
          }
        ]}
        resultsCount={filteredUsers.length}
        resultsType="users"
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        showViewToggle={true}
      />

      {/* Content */}
      <UserList
        users={paginatedUsers}
        loading={loading}
        onFollow={(user) => console.log('Follow user:', user)}
        onVisitProfile={(user) => console.log('Visit profile:', user)}
        showEmail={true}
        showPhone={false}
        showLocation={false}
        searchable={false} // We already have search above
        emptyTitle="No users found"
        emptyDescription={searchTerm ? "Try adjusting your search criteria" : "Users will appear here once they register"}
        customActions={[
          {
            id: 'view',
            label: 'View Details',
            icon: FiEye,
            onClick: (user) => console.log('View user:', user)
          },
          {
            id: 'edit',
            label: 'Edit User',
            icon: FiEdit,
            onClick: (user) => console.log('Edit user:', user)
          },
          {
            id: 'delete',
            label: 'Delete User',
            icon: FiTrash2,
            onClick: (user) => console.log('Delete user:', user)
          }
        ]}
      />

      {/* Legacy view mode toggle - keeping for now */}
      {false && viewMode === "table" ? (
        /* Table View */
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Joined
                </th>
                <th className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredUsers.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-16 text-center">
                    <FiUsers className="mx-auto text-gray-400 mb-4" size={48} />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No users found</h3>
                    <p className="text-gray-500 dark:text-gray-400">
                      {searchTerm ? "Try adjusting your search criteria" : "Users will appear here once they register"}
                    </p>
                  </td>
                </tr>
              ) : (
                paginatedUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-violet-100 dark:bg-violet-900/30 rounded-full flex items-center justify-center mr-3">
                          <span className="text-sm font-medium text-violet-600 dark:text-violet-400">
                            {user.username?.charAt(0).toUpperCase() || 'U'}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {user.username || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {user.email || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`
                        inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        ${user.user_type === 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                          user.user_type === 'teacher' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          user.user_type === 'student' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          user.user_type === 'institute' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                          user.user_type === 'mentor' ? 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                        }
                      `}>
                        {user.user_type?.charAt(0).toUpperCase() + user.user_type?.slice(1) || 'Unknown'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`
                        inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                        ${user.is_email_verified
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }
                      `}>
                        {user.is_email_verified ? 'Verified' : 'Unverified'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                          <FiEye className="w-4 h-4" />
                        </button>
                        <button className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                          <FiEdit className="w-4 h-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                          <FiTrash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      ) : (
        /* Grid View */
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredUsers.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <FiUsers className="mx-auto text-gray-400 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No users found</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                {searchTerm ? "Try adjusting your search criteria" : "Users will appear here once they register"}
              </p>
            </div>
          ) : (
            paginatedUsers.map((user) => (
              <UserCard key={user.id} user={user} />
            ))
          )}
        </div>
      )}

      {/* Pagination */}
      {filteredUsers.length > itemsPerPage && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredUsers.length)} of {filteredUsers.length} users
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              className="btn btn-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="btn btn-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default AdminUsers;
