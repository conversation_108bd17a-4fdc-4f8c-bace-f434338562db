import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiPlus,
  FiCalendar,
  FiUsers,
  FiMapPin,
  FiClock,
  FiEdit,
  FiTrash2,
  FiEye,
  FiFilter,
  FiRefreshCw,
  FiGrid,
  FiList,
  FiStar,
  FiDollarSign,
  FiMic,
  FiAward,
  FiBookOpen,
  FiVideo,
  FiTrendingUp,
  FiX
} from 'react-icons/fi';
import {
  fetchInstituteEvents,
  createInstituteEvent,
  deleteInstituteEvent,
  publishInstituteEvent,
  selectEvents,
  selectEventsLoading,
  selectEventsError,
  selectCreateLoading,
  selectCreateSuccess,
  clearErrors,
  clearSuccessStates
} from '../../store/slices/InstituteEventsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import SearchFilterCard from '../../components/ui/SearchFilterCard';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';

function InstituteEvents() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDate, setFilterDate] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // 'grid', 'list', 'calendar'
  const [showBannerSlider, setShowBannerSlider] = useState(true);

  // Redux selectors
  const events = useSelector(selectEvents);
  const eventsLoading = useSelector(selectEventsLoading);
  const eventsError = useSelector(selectEventsError);
  const createLoading = useSelector(selectCreateLoading);
  const createSuccess = useSelector(selectCreateSuccess);

  // Mock data for featured events (banners)
  const [featuredEvents] = useState([
    {
      id: 'featured-1',
      title: 'Annual Tech Conference 2024',
      short_description: 'Join industry leaders for cutting-edge technology discussions',
      banner_image_url: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=800&h=400&fit=crop',
      category: 'conference',
      start_datetime: '2024-09-15T09:00:00Z',
      location: { name: 'Main Auditorium', address: 'Campus Center' },
      is_featured: true,
      ticket_price: 50
    },
    {
      id: 'featured-2',
      title: 'AI Workshop Series',
      short_description: 'Hands-on machine learning and AI development workshop',
      banner_image_url: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=400&fit=crop',
      category: 'workshop',
      start_datetime: '2024-09-20T14:00:00Z',
      location: { name: 'Computer Lab', address: 'Engineering Building' },
      is_featured: true,
      ticket_price: 25
    },
    {
      id: 'featured-3',
      title: 'Programming Competition',
      short_description: 'Test your coding skills in our annual programming challenge',
      banner_image_url: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=800&h=400&fit=crop',
      category: 'competition',
      start_datetime: '2024-09-25T10:00:00Z',
      location: { name: 'Virtual Platform', address: 'Online Event' },
      is_featured: true,
      ticket_price: 0
    }
  ]);

  // Load events on component mount
  useEffect(() => {
    dispatch(fetchInstituteEvents({ skip: 0, limit: 20 }));
  }, [dispatch]);

  // Handle create success
  useEffect(() => {
    if (createSuccess) {
      dispatch(clearSuccessStates());
      // Optionally show success message
    }
  }, [createSuccess, dispatch]);

  // Search and filter handlers
  const handleSearchChange = (value) => {
    setSearchTerm(value);
  };

  const handleSearchSubmit = (value) => {
    setSearchTerm(value);
  };

  const handleSearchClear = () => {
    setSearchTerm('');
  };

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await dispatch(fetchInstituteEvents({ skip: 0, limit: 20 }));
    } finally {
      setRefreshing(false);
    }
  };

  // Handle filter change
  const handleFilterChange = (newFilter) => {
    setFilterStatus(newFilter);
    dispatch(fetchInstituteEvents({
      skip: 0,
      limit: 20,
      status: newFilter === 'all' ? '' : newFilter
    }));
  };

  // Filter events based on search and filters
  const filteredEvents = (events.events || []).filter(event => {
    const matchesSearch = !searchTerm ||
      event.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.location?.name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = filterCategory === 'all' || event.category === filterCategory;
    const matchesStatus = filterStatus === 'all' || event.status === filterStatus;

    let matchesDate = true;
    if (filterDate !== 'all') {
      const eventDate = new Date(event.start_datetime);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

      switch (filterDate) {
        case 'today':
          matchesDate = eventDate >= today && eventDate < tomorrow;
          break;
        case 'tomorrow':
          matchesDate = eventDate >= tomorrow && matchesDate < new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000);
          break;
        case 'this_week':
          matchesDate = eventDate >= today && eventDate < nextWeek;
          break;
        case 'upcoming':
          matchesDate = eventDate > now;
          break;
        default:
          matchesDate = true;
      }
    }

    return matchesSearch && matchesCategory && matchesStatus && matchesDate;
  });

  // Get category icon
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'workshop': return FiBookOpen;
      case 'conference': return FiMic;
      case 'webinar': return FiVideo;
      case 'competition': return FiAward;
      default: return FiCalendar;
    }
  };

  // Get category color
  const getCategoryColor = (category) => {
    switch (category) {
      case 'workshop': return 'bg-blue-100 text-blue-800';
      case 'conference': return 'bg-purple-100 text-purple-800';
      case 'webinar': return 'bg-green-100 text-green-800';
      case 'competition': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle delete event
  const handleDeleteEvent = async (eventId) => {
    if (window.confirm('Are you sure you want to delete this event?')) {
      await dispatch(deleteInstituteEvent(eventId));
    }
  };

  // Handle publish event
  const handlePublishEvent = async (eventId) => {
    await dispatch(publishInstituteEvent(eventId));
  };

  // Format date
  const formatEventDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, yyyy');
  };

  const formatEventTime = (dateString) => {
    return format(new Date(dateString), 'h:mm a');
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      dispatch(clearErrors());
    };
  }, [dispatch]);

  if (eventsError) {
    return <ErrorMessage message={eventsError} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Event Management</h1>
          <p className="text-gray-600">Manage all your institute events including workshops, conferences, webinars, and competitions</p>
          <div className="mt-2 flex items-center text-sm text-blue-600">
            <FiAward className="h-4 w-4 mr-1" />
            <span>Competitions are managed here as event categories</span>
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <FiRefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={() => navigate('/institute/events/create')}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Create Event
          </button>
        </div>
      </div>

      {/* Featured Events Banner Slider */}
      {showBannerSlider && featuredEvents.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="flex justify-between items-center p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <FiStar className="h-5 w-5 mr-2 text-yellow-500" />
              Featured Events
            </h2>
            <button
              onClick={() => setShowBannerSlider(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <FiX className="h-5 w-5" />
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
            {featuredEvents.map((event) => (
              <div key={event.id} className="relative group cursor-pointer">
                <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg overflow-hidden">
                  <img
                    src={event.banner_image_url}
                    alt={event.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                    <div className="flex items-center justify-between mb-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded ${getCategoryColor(event.category)}`}>
                        {event.category}
                      </span>
                      {event.ticket_price > 0 && (
                        <span className="flex items-center text-sm font-medium">
                          <FiDollarSign className="h-4 w-4 mr-1" />
                          {event.ticket_price}
                        </span>
                      )}
                    </div>
                    <h3 className="text-lg font-semibold mb-1">{event.title}</h3>
                    <p className="text-sm text-gray-200 mb-2">{event.short_description}</p>
                    <div className="flex items-center text-sm">
                      <FiCalendar className="h-4 w-4 mr-1" />
                      {formatEventDate(event.start_datetime)}
                      <FiMapPin className="h-4 w-4 ml-3 mr-1" />
                      {event.location.name}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <SearchFilterCard
        searchValue={searchTerm}
        onSearchChange={handleSearchChange}
        onSearchSubmit={handleSearchSubmit}
        onSearchClear={handleSearchClear}
        searchPlaceholder="Search events by title, description, or location..."
        filters={[
          {
            label: 'Category',
            value: filterCategory,
            onChange: setFilterCategory,
            options: [
              { value: 'all', label: 'All Categories' },
              { value: 'workshop', label: 'Workshops' },
              { value: 'conference', label: 'Conferences' },
              { value: 'webinar', label: 'Webinars' },
              { value: 'competition', label: 'Competitions' }
            ]
          },
          {
            label: 'Status',
            value: filterStatus,
            onChange: setFilterStatus,
            options: [
              { value: 'all', label: 'All Status' },
              { value: 'published', label: 'Published' },
              { value: 'draft', label: 'Draft' },
              { value: 'cancelled', label: 'Cancelled' }
            ]
          },
          {
            label: 'Date',
            value: filterDate,
            onChange: setFilterDate,
            options: [
              { value: 'all', label: 'All Dates' },
              { value: 'today', label: 'Today' },
              { value: 'tomorrow', label: 'Tomorrow' },
              { value: 'this_week', label: 'This Week' },
              { value: 'upcoming', label: 'Upcoming' }
            ]
          }
        ]}
        resultsCount={filteredEvents.length}
        resultsType="events"
        showViewToggle={true}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        viewOptions={[
          { value: 'grid', label: 'Grid', icon: FiGrid },
          { value: 'list', label: 'List', icon: FiList },
          { value: 'calendar', label: 'Calendar', icon: FiCalendar }
        ]}
      />

      {/* Events List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Events ({filteredEvents.length})
          </h3>
        </div>
        <div className="p-6">
          {eventsLoading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner size="lg" />
            </div>
          ) : filteredEvents.length > 0 ? (
            <div className={viewMode === 'list' ? 'space-y-4' : 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'}>
              {filteredEvents.map((event) => (
                <div key={event.id} className={`border border-gray-200 rounded-lg hover:shadow-md transition-shadow ${
                  viewMode === 'list' ? 'p-4' : 'p-4'
                }`}>
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="text-lg font-medium text-gray-900 truncate">{event.title}</h4>
                        <span className={`px-2 py-1 text-xs font-medium rounded ${getCategoryColor(event.category || 'other')}`}>
                          {event.category || 'Event'}
                        </span>
                      </div>
                      {viewMode === 'list' && (
                        <p className="text-sm text-gray-600 mb-2">{event.description}</p>
                      )}
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(event.status)}`}>
                      {event.status}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">{event.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <FiCalendar className="h-4 w-4 mr-2" />
                      {formatDate(event.startDateTime)}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <FiMapPin className="h-4 w-4 mr-2" />
                      {event.location?.venue || 'TBD'}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <FiUsers className="h-4 w-4 mr-2" />
                      {event.attendeesCount || 0} attendees
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => navigate(`/institute/events/${event.id}`)}
                        className="text-blue-600 hover:text-blue-800"
                        title="View Details"
                      >
                        <FiEye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => navigate(`/institute/events/${event.id}/edit`)}
                        className="text-gray-600 hover:text-gray-800"
                        title="Edit Event"
                      >
                        <FiEdit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteEvent(event.id)}
                        className="text-red-600 hover:text-red-800"
                        title="Delete Event"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>
                    
                    {event.status === 'draft' && (
                      <button
                        onClick={() => handlePublishEvent(event.id)}
                        className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                      >
                        Publish
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FiCalendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {searchTerm || filterCategory !== 'all' || filterStatus !== 'all' || filterDate !== 'all'
                  ? 'No events match your criteria'
                  : 'No events found'
                }
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || filterCategory !== 'all' || filterStatus !== 'all' || filterDate !== 'all'
                  ? 'Try adjusting your search or filters.'
                  : 'Get started by creating your first event.'
                }
              </p>
              {(!searchTerm && filterCategory === 'all' && filterStatus === 'all' && filterDate === 'all') && (
                <div className="mt-6">
                  <button
                    onClick={() => navigate('/institute/events/create')}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <FiPlus className="h-4 w-4 mr-2" />
                    Create Event
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Load More */}
      {events.pagination?.hasMore && (
        <div className="text-center">
          <button
            onClick={() => dispatch(fetchInstituteEvents({
              skip: events.pagination.skip,
              limit: 20,
              status: filterStatus === 'all' ? '' : filterStatus
            }))}
            disabled={eventsLoading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {eventsLoading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Loading...
              </>
            ) : (
              'Load More'
            )}
          </button>
        </div>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiCalendar className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Events</p>
              <p className="text-2xl font-semibold text-gray-900">{filteredEvents.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiTrendingUp className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Published</p>
              <p className="text-2xl font-semibold text-gray-900">
                {filteredEvents.filter(e => e.status === 'published').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiAward className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Competitions</p>
              <p className="text-2xl font-semibold text-gray-900">
                {filteredEvents.filter(e => e.category === 'competition').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiStar className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Featured</p>
              <p className="text-2xl font-semibold text-gray-900">
                {filteredEvents.filter(e => e.is_featured).length}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default InstituteEvents;
