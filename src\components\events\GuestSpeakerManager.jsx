import React, { useState } from 'react';
import {
  FiUser,
  FiPlus,
  FiEdit,
  FiTrash2,
  FiSave,
  FiX,
  FiMail,
  FiPhone,
  FiLinkedin,
  FiGlobe,
  FiStar,
  FiAward,
  FiBookOpen
} from 'react-icons/fi';

const GuestSpeakerManager = ({ 
  speakers = [], 
  onAddSpeaker, 
  onUpdateSpeaker, 
  onDeleteSpeaker,
  className = '' 
}) => {
  const [isAddingSpeaker, setIsAddingSpeaker] = useState(false);
  const [editingSpeaker, setEditingSpeaker] = useState(null);
  const [newSpeaker, setNewSpeaker] = useState({
    name: '',
    title: '',
    company: '',
    bio: '',
    email: '',
    phone: '',
    linkedin: '',
    website: '',
    profile_image_url: '',
    expertise_areas: [],
    speaking_topics: [],
    is_keynote: false,
    fee: '',
    availability_notes: ''
  });

  const expertiseOptions = [
    'Technology', 'Business', 'Education', 'Healthcare', 'Finance',
    'Marketing', 'Leadership', 'Innovation', 'Entrepreneurship', 'AI/ML',
    'Data Science', 'Cybersecurity', 'Sustainability', 'Design', 'Research'
  ];

  const handleAddSpeaker = () => {
    if (newSpeaker.name && newSpeaker.title) {
      onAddSpeaker(newSpeaker);
      setNewSpeaker({
        name: '',
        title: '',
        company: '',
        bio: '',
        email: '',
        phone: '',
        linkedin: '',
        website: '',
        profile_image_url: '',
        expertise_areas: [],
        speaking_topics: [],
        is_keynote: false,
        fee: '',
        availability_notes: ''
      });
      setIsAddingSpeaker(false);
    }
  };

  const handleUpdateSpeaker = (speakerId, updatedData) => {
    onUpdateSpeaker(speakerId, updatedData);
    setEditingSpeaker(null);
  };

  const handleExpertiseToggle = (expertise, isEditing = false) => {
    if (isEditing && editingSpeaker) {
      const updatedExpertise = editingSpeaker.expertise_areas.includes(expertise)
        ? editingSpeaker.expertise_areas.filter(e => e !== expertise)
        : [...editingSpeaker.expertise_areas, expertise];
      setEditingSpeaker({ ...editingSpeaker, expertise_areas: updatedExpertise });
    } else {
      const updatedExpertise = newSpeaker.expertise_areas.includes(expertise)
        ? newSpeaker.expertise_areas.filter(e => e !== expertise)
        : [...newSpeaker.expertise_areas, expertise];
      setNewSpeaker({ ...newSpeaker, expertise_areas: updatedExpertise });
    }
  };

  const SpeakerForm = ({ speaker, isEditing, onSave, onCancel }) => (
    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Full Name *
          </label>
          <input
            type="text"
            value={speaker.name}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, name: e.target.value })
              : setNewSpeaker({ ...speaker, name: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="e.g., Dr. Jane Smith"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Title/Position *
          </label>
          <input
            type="text"
            value={speaker.title}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, title: e.target.value })
              : setNewSpeaker({ ...speaker, title: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="e.g., Chief Technology Officer"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Company/Organization
          </label>
          <input
            type="text"
            value={speaker.company}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, company: e.target.value })
              : setNewSpeaker({ ...speaker, company: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="e.g., Tech Corp Inc."
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            type="email"
            value={speaker.email}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, email: e.target.value })
              : setNewSpeaker({ ...speaker, email: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone
          </label>
          <input
            type="tel"
            value={speaker.phone}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, phone: e.target.value })
              : setNewSpeaker({ ...speaker, phone: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="+****************"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Speaking Fee
          </label>
          <input
            type="number"
            value={speaker.fee}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, fee: e.target.value })
              : setNewSpeaker({ ...speaker, fee: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="0"
          />
        </div>
        
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Bio/Description
          </label>
          <textarea
            value={speaker.bio}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, bio: e.target.value })
              : setNewSpeaker({ ...speaker, bio: e.target.value })
            }
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Brief biography and background..."
          />
        </div>
        
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Expertise Areas
          </label>
          <div className="flex flex-wrap gap-2">
            {expertiseOptions.map((expertise) => {
              const isSelected = speaker.expertise_areas.includes(expertise);
              return (
                <button
                  key={expertise}
                  type="button"
                  onClick={() => handleExpertiseToggle(expertise, isEditing)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    isSelected
                      ? 'bg-blue-100 text-blue-800 border border-blue-300'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {expertise}
                </button>
              );
            })}
          </div>
        </div>
        
        <div className="md:col-span-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_keynote"
              checked={speaker.is_keynote}
              onChange={(e) => isEditing 
                ? setEditingSpeaker({ ...speaker, is_keynote: e.target.checked })
                : setNewSpeaker({ ...speaker, is_keynote: e.target.checked })
              }
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_keynote" className="ml-2 block text-sm text-gray-700">
              Keynote Speaker
            </label>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end space-x-3 mt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <FiX className="h-4 w-4 mr-2 inline" />
          Cancel
        </button>
        <button
          type="button"
          onClick={onSave}
          className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
        >
          <FiSave className="h-4 w-4 mr-2 inline" />
          {isEditing ? 'Update' : 'Add'} Speaker
        </button>
      </div>
    </div>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Guest Speakers</h3>
        <button
          onClick={() => setIsAddingSpeaker(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <FiPlus className="h-4 w-4 mr-2" />
          Add Speaker
        </button>
      </div>

      {/* Add Speaker Form */}
      {isAddingSpeaker && (
        <SpeakerForm
          speaker={newSpeaker}
          isEditing={false}
          onSave={handleAddSpeaker}
          onCancel={() => setIsAddingSpeaker(false)}
        />
      )}

      {/* Speakers List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {speakers.map((speaker) => (
          <div key={speaker.id} className="bg-white border border-gray-200 rounded-lg p-4">
            {editingSpeaker?.id === speaker.id ? (
              <SpeakerForm
                speaker={editingSpeaker}
                isEditing={true}
                onSave={() => handleUpdateSpeaker(speaker.id, editingSpeaker)}
                onCancel={() => setEditingSpeaker(null)}
              />
            ) : (
              <div>
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {speaker.profile_image_url ? (
                        <img
                          src={speaker.profile_image_url}
                          alt={speaker.name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                          <FiUser className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h4 className="text-lg font-medium text-gray-900 truncate">{speaker.name}</h4>
                        {speaker.is_keynote && (
                          <FiStar className="h-4 w-4 text-yellow-500" title="Keynote Speaker" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{speaker.title}</p>
                      {speaker.company && (
                        <p className="text-sm text-gray-500">{speaker.company}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setEditingSpeaker(speaker)}
                      className="text-gray-600 hover:text-gray-800"
                      title="Edit Speaker"
                    >
                      <FiEdit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onDeleteSpeaker(speaker.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Delete Speaker"
                    >
                      <FiTrash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {speaker.bio && (
                  <p className="text-sm text-gray-600 mb-3 line-clamp-3">{speaker.bio}</p>
                )}

                {speaker.expertise_areas && speaker.expertise_areas.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {speaker.expertise_areas.slice(0, 3).map((area) => (
                      <span
                        key={area}
                        className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                      >
                        {area}
                      </span>
                    ))}
                    {speaker.expertise_areas.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{speaker.expertise_areas.length - 3} more
                      </span>
                    )}
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="flex space-x-2">
                    {speaker.email && (
                      <a
                        href={`mailto:${speaker.email}`}
                        className="text-gray-400 hover:text-gray-600"
                        title="Email"
                      >
                        <FiMail className="h-4 w-4" />
                      </a>
                    )}
                    {speaker.phone && (
                      <a
                        href={`tel:${speaker.phone}`}
                        className="text-gray-400 hover:text-gray-600"
                        title="Phone"
                      >
                        <FiPhone className="h-4 w-4" />
                      </a>
                    )}
                    {speaker.linkedin && (
                      <a
                        href={speaker.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-gray-600"
                        title="LinkedIn"
                      >
                        <FiLinkedin className="h-4 w-4" />
                      </a>
                    )}
                  </div>
                  {speaker.fee > 0 && (
                    <span className="text-sm font-medium text-green-600">
                      ${speaker.fee}
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {speakers.length === 0 && !isAddingSpeaker && (
        <div className="text-center py-8 text-gray-500">
          <FiUser className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p>No speakers added yet. Add your first guest speaker.</p>
        </div>
      )}
    </div>
  );
};

export default GuestSpeakerManager;
