import React from 'react';
import { FiLinkedin, FiFacebook, FiTwitter, FiExternalLink } from 'react-icons/fi';

const InstituteSocialLinksForm = ({
  formData,
  onChange,
  isEditing,
  fieldErrors,
  hasAttemptedSubmit
}) => {
  const getFieldError = (fieldName) => {
    return hasAttemptedSubmit && fieldErrors[fieldName];
  };

  const socialPlatforms = [
    {
      key: 'linkedin_url',
      label: 'LinkedIn',
      icon: FiLinkedin,
      placeholder: 'https://linkedin.com/company/your-institute',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      key: 'facebook_url',
      label: 'Facebook',
      icon: FiFacebook,
      placeholder: 'https://facebook.com/your-institute',
      color: 'text-blue-700',
      bgColor: 'bg-blue-50'
    },
    {
      key: 'twitter_url',
      label: 'Twitter',
      icon: FiTwitter,
      placeholder: 'https://twitter.com/your-institute',
      color: 'text-sky-500',
      bgColor: 'bg-sky-50'
    }
  ];

  const renderSocialField = (platform) => {
    const error = getFieldError(platform.key);
    const Icon = platform.icon;
    const value = formData[platform.key] || '';
    
    return (
      <div key={platform.key}>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {platform.label}
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Icon className={`h-4 w-4 ${platform.color}`} />
          </div>
          <input
            type="url"
            name={platform.key}
            value={value}
            onChange={onChange}
            disabled={!isEditing}
            placeholder={platform.placeholder}
            className={`w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              !isEditing ? 'bg-gray-50 text-gray-600' : 'bg-white'
            } ${error ? 'border-red-300' : 'border-gray-300'}`}
          />
          {value && !isEditing && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <a
                href={value}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-gray-600"
              >
                <FiExternalLink className="h-4 w-4" />
              </a>
            </div>
          )}
        </div>
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center">
          <FiLinkedin className="h-5 w-5 text-blue-600 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">Social Media Links</h3>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          Connect your social media profiles to increase visibility
        </p>
      </div>

      <div className="p-6 space-y-6">
        {socialPlatforms.map(platform => renderSocialField(platform))}

        {/* Social Links Preview */}
        {!isEditing && (formData.linkedin_url || formData.facebook_url || formData.twitter_url) && (
          <div className="border-t pt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-4">Social Links</h4>
            <div className="flex flex-wrap gap-3">
              {socialPlatforms.map(platform => {
                const url = formData[platform.key];
                if (!url) return null;
                
                const Icon = platform.icon;
                return (
                  <a
                    key={platform.key}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium ${platform.bgColor} ${platform.color} hover:opacity-80 transition-opacity`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {platform.label}
                    <FiExternalLink className="h-3 w-3 ml-2" />
                  </a>
                );
              })}
            </div>
          </div>
        )}

        {/* Help Text */}
        {isEditing && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <FiExternalLink className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Social Media Tips
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <ul className="list-disc list-inside space-y-1">
                    <li>Use complete URLs including https://</li>
                    <li>Make sure your profiles are public or business accounts</li>
                    <li>Keep your social media content professional and up-to-date</li>
                    <li>Social links help build trust and credibility with students</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InstituteSocialLinksForm;
