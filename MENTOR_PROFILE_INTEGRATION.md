# Mentor Profile API Integration

This document outlines the changes made to handle the new mentor profile API response structure.

## New API Response Structure

The mentor profile API now returns a more comprehensive response with the following structure:

```json
{
  "user": {
    "id": "1c46388b-f066-4526-88e1-6263787946d4",
    "username": "Master Shifu",
    "email": "<EMAIL>",
    "mobile": "923351456501",
    "country": "PK",
    "profile_picture": "profile_pictures/3fb95782_20250814_050237_cb954546.jpg",
    "user_type": "mentor",
    "is_email_verified": false,
    "is_mobile_verified": false,
    "created_at": "2025-08-11T19:02:19.295145Z",
    "mentor_profile": {
      "id": "51e99453-10d7-467f-a058-955c2fa96ced",
      "user_id": "1c46388b-f066-4526-88e1-6263787946d4",
      "bio": "I am master shifu , your exams will be checked by me , no rishwat plz",
      "experience_years": 1,
      "hourly_rate": "23.00",
      "languages": ["English"],
      "availability_hours": {
        "monday": ["09:00-12:00"],
        "tuesday": ["09:00-12:00"],
        "wednesday": ["09:00-12:00"],
        "thursday": ["09:00-12:00"],
        "friday": ["09:00-12:00"],
        "saturday": ["09:00-12:00"],
        "sunday": ["OFF"]
      },
      "profile_image_url": "/static/profile_pictures/3fb95782_20250814_050237_cb954546.jpg",
      "expertise_subjects": [
        {"id": "3648cb30-191b-4505-8368-79af32369499", "name": "Computer Science"},
        {"id": "520c13cb-4e70-4c71-b5f6-87b560ba0618", "name": "Physics"}
      ],
      "preferred_subjects": [
        {"id": "3648cb30-191b-4505-8368-79af32369499", "name": "Computer Science"},
        {"id": "520c13cb-4e70-4c71-b5f6-87b560ba0618", "name": "Physics"}
      ],
      "created_at": "2025-08-14T04:34:42.249704Z",
      "updated_at": "2025-08-14T05:02:39.747741Z"
    }
  },
  "profile": {
    // Same structure as user.mentor_profile
  },
  "total_competitions": 0,
  "active_institutes": 0,
  "average_rating": null,
  "verification_status": "pending"
}
```

## Changes Made

### 1. Redux Store Updates (`src/store/slices/MentorsSlice.js`)

- Updated `fetchMentorProfile.fulfilled` and `updateMentorProfile.fulfilled` reducers
- Now properly handles the new API response structure
- Merges user data with profile data for backward compatibility
- Includes additional fields like `total_competitions`, `active_institutes`, `average_rating`, and `verification_status`
- **Added caching functionality:**
  - Profile data is cached for 5 minutes after successful API calls
  - New `fetchMentorProfileCached` thunk checks cache before making API calls
  - Cache timestamp tracking and validation
  - Cache management actions (`clearProfileCache`, `isProfileCacheValid`)

### 2. Custom Hook Updates (`src/hooks/useMentorProfile.js`)

- Updated profile initialization to handle the new data structure
- Properly extracts user and profile information
- Maintains backward compatibility with existing code
- Includes ID arrays for subjects for form compatibility
- **Added caching support:**
  - Uses `fetchMentorProfileCached` by default
  - Added `forceRefreshProfile` function to bypass cache
  - Added `clearCache` function for cache management
  - Added `isCacheValid` state flag

### 3. Component Updates

#### MentorDashboard (`src/pages/mentor/MentorDashboard.jsx`)
- Updated to use `username` instead of `first_name`
- Integrated new `MentorProfileDisplay` component
- Simplified profile overview section

#### MentorCard (`src/components/mentors/MentorCard.jsx`)
- Added support for both old and new data structures
- Properly handles nested user and profile data
- Maps verification status correctly

#### MentorDetailsPage (`src/pages/mentors/MentorDetailsPage.jsx`)
- Comprehensive update to handle new data structure
- Extracts and normalizes data from nested objects
- Updated all references to use processed data
- Improved availability hours display for new format

### 4. New Components

#### MentorProfileDisplay (`src/components/mentors/MentorProfileDisplay.jsx`)
- Reusable component for displaying mentor profile information
- Handles both old and new data structures
- Supports full and compact display modes
- Includes verification status, ratings, and statistics

#### MentorProfileTest (`src/pages/mentor/MentorProfileTest.jsx`)
- Test page for verifying API integration
- Displays raw API response and processed data
- Includes test update functionality
- Useful for debugging and development

## Usage

### Fetching Mentor Profile

```javascript
import { useDispatch } from 'react-redux';
import { fetchMentorProfile, fetchMentorProfileCached } from '../store/slices/MentorsSlice';

const dispatch = useDispatch();

// Fetch profile (with caching) - recommended
const result = await dispatch(fetchMentorProfileCached()).unwrap();

// Force fresh fetch (bypass cache)
const freshResult = await dispatch(fetchMentorProfile()).unwrap();

// Force refresh with cached function
const refreshResult = await dispatch(fetchMentorProfileCached(true)).unwrap();
```

### Updating Mentor Profile

```javascript
import { updateMentorProfile } from '../store/slices/MentorsSlice';

const updateData = {
  bio: "Updated bio",
  experience_years: 5,
  hourly_rate: 75.00,
  languages: ["English", "Spanish"],
  expertise_subject_ids: ["uuid1", "uuid2"],
  preferred_subject_ids: ["uuid1", "uuid3"]
};

const result = await dispatch(updateMentorProfile(updateData)).unwrap();
```

### Using the Profile Display Component

```javascript
import MentorProfileDisplay from '../components/mentors/MentorProfileDisplay';

// Full details
<MentorProfileDisplay mentorData={profileData} showFullDetails={true} />

// Compact view
<MentorProfileDisplay mentorData={profileData} showFullDetails={false} />
```

### Using the Custom Hook with Caching

```javascript
import { useMentorProfile } from '../hooks/useMentorProfile';

const MyComponent = () => {
  const {
    profile,
    isLoading,
    isCacheValid,
    fetchProfile,
    forceRefreshProfile,
    clearCache
  } = useMentorProfile();

  // Fetch with cache (default behavior)
  const handleRefresh = () => fetchProfile();

  // Force fresh fetch
  const handleForceRefresh = () => forceRefreshProfile();

  // Clear cache
  const handleClearCache = () => clearCache();

  return (
    <div>
      <p>Cache Valid: {isCacheValid ? 'Yes' : 'No'}</p>
      <button onClick={handleRefresh}>Refresh (Cached)</button>
      <button onClick={handleForceRefresh}>Force Refresh</button>
      <button onClick={handleClearCache}>Clear Cache</button>
    </div>
  );
};
```

### Cache Management

```javascript
import { clearProfileCache, selectIsProfileCacheValid } from '../store/slices/MentorsSlice';

// Clear cache manually
dispatch(clearProfileCache());

// Check cache validity in selector
const isCacheValid = useSelector(selectIsProfileCacheValid);
```

## Testing

1. Navigate to `/mentor/profile-test` to access the test page
2. Use the test page to verify API integration
3. Check browser console for raw API responses
4. Test both fetch and update operations

## Backward Compatibility

The changes maintain backward compatibility by:
- Keeping the same Redux state structure
- Providing fallbacks for missing data
- Supporting both old and new data formats in components
- Maintaining existing prop interfaces

## Caching Details

### Cache Configuration
- **Cache Duration**: 5 minutes (300,000 milliseconds)
- **Cache Key**: Profile data is stored in Redux state with timestamp
- **Cache Validation**: Automatic validation based on timestamp comparison

### Cache Behavior
- **First Load**: Always fetches from API and caches the result
- **Subsequent Loads**: Uses cached data if valid, otherwise fetches fresh data
- **Profile Updates**: Automatically refreshes cache with new data
- **Manual Cache Control**: Developers can force refresh or clear cache as needed

### Performance Benefits
- **Reduced API Calls**: Avoids unnecessary network requests for recent data
- **Faster Loading**: Instant display of cached profile data
- **Better UX**: Smoother navigation between pages that use profile data
- **Bandwidth Savings**: Reduces data usage for mobile users

## Notes

- The `availability_hours` format has changed from object with `start`/`end`/`available` to arrays of time strings
- Verification status is now a string enum: "pending", "verified", "rejected"
- Profile images are now served from `/static/` path
- Subject data includes both `id` and `name` fields
- **Caching is enabled by default** - use `fetchMentorProfileCached` for optimal performance
- Cache is automatically cleared when user logs out or switches accounts
