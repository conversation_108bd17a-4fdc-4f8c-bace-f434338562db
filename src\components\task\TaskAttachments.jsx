import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  uploadTaskAttachment,
  fetchTaskAttachments,
  deleteTaskAttachment,
  selectAttachments,
  selectAttachmentLoading,
  selectAttachmentError,
  clearAttachmentState
} from '../../store/slices/TaskSlice';
import { getErrorMessage } from '../../utils/helpers/errorHandler';
import {
  FiUpload,
  FiFile,
  FiDownload,
  FiTrash2,
  FiX,
  FiPaperclip,
  FiLoader,
  FiAlertCircle,
  FiCheck,
  FiImage,
  FiFileText,
  FiVideo
} from 'react-icons/fi';

/**
 * TaskAttachments Component
 * Handles file uploads, downloads, and attachment management for tasks
 * 
 * Props:
 * - taskId: ID of the task
 * - attachmentType: Type of attachment ('task' for task files, 'submission' for student submissions)
 * - canUpload: Whether user can upload files (default: true)
 * - canDelete: Whether user can delete files (default: true)
 * - maxFileSize: Maximum file size in MB (default: 10)
 * - allowedTypes: Array of allowed file types (default: all)
 * - className: Additional CSS classes
 */
const TaskAttachments = ({
  taskId,
  attachmentType = 'task',
  canUpload = true,
  canDelete = true,
  maxFileSize = 10,
  allowedTypes = [],
  className = ''
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const fileInputRef = useRef(null);

  // Redux state
  const attachments = useSelector(selectAttachments);
  const loading = useSelector(selectAttachmentLoading);
  const error = useSelector(selectAttachmentError);

  // Local state
  const [dragOver, setDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Load attachments on mount
  useEffect(() => {
    if (taskId) {
      dispatch(fetchTaskAttachments({ task_id: taskId, attachment_type: attachmentType }));
    }
    
    return () => {
      dispatch(clearAttachmentState());
    };
  }, [dispatch, taskId, attachmentType]);

  // File validation
  const validateFile = (file) => {
    const errors = [];

    // Check if file exists and has content
    if (!file || file.size === 0) {
      errors.push('File is empty or corrupted');
      return errors;
    }

    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      errors.push(`File size (${(file.size / (1024 * 1024)).toFixed(1)}MB) exceeds limit of ${maxFileSize}MB`);
    }

    // Check file name
    if (file.name.length > 255) {
      errors.push('File name is too long (max 255 characters)');
    }

    // Check for invalid characters in filename
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(file.name)) {
      errors.push('File name contains invalid characters');
    }

    // Check file type
    if (allowedTypes.length > 0) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (!fileExtension) {
        errors.push('File must have an extension');
      } else if (!allowedTypes.includes(fileExtension)) {
        errors.push(`File type .${fileExtension} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
      }
    }

    return errors;
  };

  // Handle file selection
  const handleFileSelect = (files) => {
    const validFiles = [];
    const invalidFiles = [];

    Array.from(files).forEach(file => {
      const errors = validateFile(file);
      if (errors.length > 0) {
        invalidFiles.push({ file, errors });
      } else {
        validFiles.push(file);
      }
    });

    // Show errors for invalid files
    if (invalidFiles.length > 0) {
      const errorMessage = invalidFiles.map(({ file, errors }) =>
        `${file.name}: ${errors.join(', ')}`
      ).join('\n');
      alert(`Cannot upload the following files:\n${errorMessage}`);
    }

    // Upload valid files
    validFiles.forEach(file => uploadFile(file));
  };

  // Upload file
  const uploadFile = async (file) => {
    try {
      setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));

      await dispatch(uploadTaskAttachment({
        task_id: taskId,
        file,
        attachment_type: attachmentType
      })).unwrap();

      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[file.name];
        return newProgress;
      });

      // Refresh attachments list
      dispatch(fetchTaskAttachments({ task_id: taskId, attachment_type: attachmentType }));

    } catch (error) {
      console.error('Upload failed:', error);

      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[file.name];
        return newProgress;
      });

      // Show user-friendly error message
      const errorMessage = error?.message || error?.detail || 'Failed to upload file';
      alert(`Failed to upload "${file.name}": ${errorMessage}`);
    }
  };

  // Handle file input change
  const handleFileInputChange = (e) => {
    if (e.target.files.length > 0) {
      handleFileSelect(e.target.files);
      e.target.value = ''; // Reset input
    }
  };

  // Handle drag and drop
  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    if (e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  // Delete attachment
  const handleDelete = async (attachmentId) => {
    if (window.confirm('Are you sure you want to delete this attachment?')) {
      try {
        await dispatch(deleteTaskAttachment({
          task_id: taskId,
          attachment_id: attachmentId
        })).unwrap();
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
  };

  // Helper function to get filename from attachment object
  const getAttachmentFilename = (attachment) => {
    return attachment.filename || attachment.file_name || attachment.name || attachment.original_name || 'Unknown File';
  };

  // Download attachment
  const handleDownload = (attachment) => {
    if (attachment.download_url) {
      const link = document.createElement('a');
      link.href = attachment.download_url;
      link.download = getAttachmentFilename(attachment);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Get file icon and color
  const getFileIcon = (filename) => {
    if (!filename || typeof filename !== 'string') {
      return { icon: <FiFile className="w-5 h-5" />, color: 'text-gray-500', bgColor: 'bg-gray-100 dark:bg-gray-900/20' };
    }
    const extension = filename.split('.').pop()?.toLowerCase() || '';

    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
      return { icon: <FiImage className="w-5 h-5" />, color: 'text-blue-500', bgColor: 'bg-blue-100 dark:bg-blue-900/20' };
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(extension)) {
      return { icon: <FiVideo className="w-5 h-5" />, color: 'text-purple-500', bgColor: 'bg-purple-100 dark:bg-purple-900/20' };
    } else if (['pdf'].includes(extension)) {
      return { icon: <FiFileText className="w-5 h-5" />, color: 'text-red-500', bgColor: 'bg-red-100 dark:bg-red-900/20' };
    } else if (['doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
      return { icon: <FiFileText className="w-5 h-5" />, color: 'text-blue-600', bgColor: 'bg-blue-100 dark:bg-blue-900/20' };
    } else if (['xls', 'xlsx', 'csv', 'ods'].includes(extension)) {
      return { icon: <FiFileText className="w-5 h-5" />, color: 'text-green-600', bgColor: 'bg-green-100 dark:bg-green-900/20' };
    } else if (['ppt', 'pptx', 'odp'].includes(extension)) {
      return { icon: <FiFileText className="w-5 h-5" />, color: 'text-orange-600', bgColor: 'bg-orange-100 dark:bg-orange-900/20' };
    } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
      return { icon: <FiFile className="w-5 h-5" />, color: 'text-yellow-600', bgColor: 'bg-yellow-100 dark:bg-yellow-900/20' };
    } else {
      return { icon: <FiFile className="w-5 h-5" />, color: 'text-gray-500', bgColor: 'bg-gray-100 dark:bg-gray-700' };
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return '0 Bytes';
    if (typeof bytes !== 'number') return 'Unknown size';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`${bgPrimary} rounded-lg border ${borderColor} ${className}`}>
      {/* Header */}
      <div className={`px-4 py-3 border-b ${borderColor} ${bgSecondary}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FiPaperclip className={`w-5 h-5 ${textPrimary}`} />
            <h3 className={`font-medium ${textPrimary}`}>
              {attachmentType === 'submission' ? 'Submission Files' : 'Task Attachments'}
            </h3>
            <span className={`text-sm ${textSecondary}`}>
              ({attachments.length})
            </span>
          </div>
          
          {canUpload && (
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={loading}
              className="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-2 text-sm"
            >
              {loading ? (
                <FiLoader className="w-4 h-4 animate-spin" />
              ) : (
                <FiUpload className="w-4 h-4" />
              )}
              Upload
            </button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="px-4 py-3 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <FiAlertCircle className="w-4 h-4" />
            <span className="text-sm">
              {getErrorMessage(error)}
            </span>
          </div>
        </div>
      )}

      {/* Upload Area */}
      {canUpload && (
        <div className={`p-6 border-b ${borderColor}`}>
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            className={`relative border-2 border-dashed rounded-lg p-8 transition-all duration-200 ${
              dragOver
                ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                : `border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-600 ${bgSecondary}`
            }`}
          >
            <div className="text-center">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-4 ${
                dragOver ? 'bg-blue-100 dark:bg-blue-900/40' : 'bg-gray-100 dark:bg-gray-700'
              }`}>
                <FiUpload className={`w-6 h-6 ${dragOver ? 'text-blue-600' : textSecondary}`} />
              </div>

              <h4 className={`text-sm font-medium ${textPrimary} mb-2`}>
                {dragOver ? 'Drop files here' : 'Upload files'}
              </h4>

              <p className={`text-sm ${textSecondary} mb-4`}>
                Drag and drop files here, or{' '}
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="text-blue-600 hover:text-blue-700 font-medium underline"
                >
                  browse
                </button>
              </p>

              <div className={`text-xs ${textSecondary} space-y-1`}>
                <p>Maximum file size: {maxFileSize}MB</p>
                {allowedTypes.length > 0 && (
                  <p>Supported formats: {allowedTypes.map(type => `.${type}`).join(', ')}</p>
                )}
              </div>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              multiple
              onChange={handleFileInputChange}
              className="hidden"
              accept={allowedTypes.length > 0 ? allowedTypes.map(type => `.${type}`).join(',') : undefined}
            />
          </div>
        </div>
      )}

      {/* Upload Progress */}
      {Object.keys(uploadProgress).length > 0 && (
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          {Object.entries(uploadProgress).map(([filename, progress]) => (
            <div key={filename} className="mb-2 last:mb-0">
              <div className="flex items-center justify-between text-sm">
                <span className={textSecondary}>{filename}</span>
                <span className={textSecondary}>Uploading...</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '50%' }}></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Attachments List */}
      <div className="p-4">
        {loading && attachments.length === 0 ? (
          <div className="text-center py-8">
            <FiLoader className={`w-6 h-6 animate-spin mx-auto mb-2 ${textSecondary}`} />
            <p className={`text-sm ${textSecondary}`}>Loading attachments...</p>
          </div>
        ) : attachments.length === 0 ? (
          <div className="text-center py-8">
            <FiFile className={`w-8 h-8 mx-auto mb-2 ${textSecondary} opacity-50`} />
            <p className={`text-sm ${textSecondary}`}>No attachments yet</p>
          </div>
        ) : (
          <div className="space-y-2">
            {attachments.map((attachment) => {
              const filename = getAttachmentFilename(attachment);
              const fileInfo = getFileIcon(filename);
              return (
                <div
                  key={attachment.id}
                  className={`flex items-center justify-between p-4 rounded-lg border ${borderColor} hover:shadow-sm transition-all duration-200 ${bgPrimary}`}
                >
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className={`p-2 rounded-lg ${fileInfo.bgColor}`}>
                      <div className={fileInfo.color}>
                        {fileInfo.icon}
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className={`text-sm font-medium ${textPrimary} truncate`}>
                        {filename}
                      </p>
                      <p className={`text-xs ${textSecondary}`}>
                        {attachment.file_size ? formatFileSize(attachment.file_size) : 'Unknown size'} • {attachment.uploaded_at ? new Date(attachment.uploaded_at).toLocaleDateString() : 'Unknown date'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleDownload(attachment)}
                      className={`p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ${textSecondary} hover:text-blue-600`}
                      title="Download"
                    >
                      <FiDownload className="w-4 h-4" />
                    </button>

                    {canDelete && (
                      <button
                        onClick={() => handleDelete(attachment.id)}
                        className={`p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ${textSecondary} hover:text-red-600`}
                        title="Delete"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskAttachments;
