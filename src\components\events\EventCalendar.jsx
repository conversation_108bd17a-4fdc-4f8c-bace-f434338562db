import React, { useState } from 'react';
import {
  FiChevronLeft,
  FiChevronRight,
  FiCalendar,
  FiClock,
  FiMapPin,
  FiUsers,
  FiStar,
  FiAward
} from 'react-icons/fi';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, isToday } from 'date-fns';

const EventCalendar = ({ events = [], onEventClick, className = '' }) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Get events for a specific day
  const getEventsForDay = (day) => {
    return events.filter(event => {
      const eventDate = new Date(event.start_datetime || event.startDateTime);
      return isSameDay(eventDate, day);
    });
  };

  // Navigate months
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  // Get category color
  const getCategoryColor = (category) => {
    switch (category) {
      case 'workshop': return 'bg-blue-500';
      case 'conference': return 'bg-purple-500';
      case 'webinar': return 'bg-green-500';
      case 'competition': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const formatTime = (dateString) => {
    return format(new Date(dateString), 'h:mm a');
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Calendar Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-semibold text-gray-900">
            {format(currentDate, 'MMMM yyyy')}
          </h2>
          <button
            onClick={goToToday}
            className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            Today
          </button>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={goToPreviousMonth}
            className="p-2 hover:bg-gray-100 rounded-md"
          >
            <FiChevronLeft className="h-5 w-5" />
          </button>
          <button
            onClick={goToNextMonth}
            className="p-2 hover:bg-gray-100 rounded-md"
          >
            <FiChevronRight className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="p-4">
        {/* Days of Week Header */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((day) => {
            const dayEvents = getEventsForDay(day);
            const isCurrentMonth = isSameMonth(day, currentDate);
            const isDayToday = isToday(day);

            return (
              <div
                key={day.toISOString()}
                className={`min-h-[100px] p-2 border border-gray-100 rounded-md ${
                  isCurrentMonth ? 'bg-white' : 'bg-gray-50'
                } ${isDayToday ? 'ring-2 ring-blue-500' : ''}`}
              >
                <div className={`text-sm font-medium mb-1 ${
                  isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                } ${isDayToday ? 'text-blue-600' : ''}`}>
                  {format(day, 'd')}
                </div>

                {/* Events for this day */}
                <div className="space-y-1">
                  {dayEvents.slice(0, 3).map((event) => (
                    <div
                      key={event.id}
                      onClick={() => onEventClick?.(event)}
                      className="cursor-pointer group"
                    >
                      <div className={`text-xs p-1 rounded text-white truncate ${getCategoryColor(event.category)} group-hover:opacity-80`}>
                        <div className="flex items-center space-x-1">
                          {event.is_featured && <FiStar className="h-3 w-3 flex-shrink-0" />}
                          {event.is_competition && <FiAward className="h-3 w-3 flex-shrink-0" />}
                          <span className="truncate">{event.title}</span>
                        </div>
                        <div className="text-xs opacity-90">
                          {formatTime(event.start_datetime || event.startDateTime)}
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {dayEvents.length > 3 && (
                    <div className="text-xs text-gray-500 text-center">
                      +{dayEvents.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Legend */}
      <div className="px-4 pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Categories:</span>
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-blue-500 rounded"></div>
                <span className="text-xs text-gray-600">Workshops</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-purple-500 rounded"></div>
                <span className="text-xs text-gray-600">Conferences</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-green-500 rounded"></div>
                <span className="text-xs text-gray-600">Webinars</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-orange-500 rounded"></div>
                <span className="text-xs text-gray-600">Competitions</span>
              </div>
            </div>
          </div>
          <div className="text-sm text-gray-500">
            {events.length} total events
          </div>
        </div>
      </div>
    </div>
  );
};

// Event Details Modal/Popup Component
export const EventDetailsModal = ({ event, isOpen, onClose }) => {
  if (!isOpen || !event) return null;

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'EEEE, MMMM d, yyyy');
  };

  const formatTime = (dateString) => {
    return format(new Date(dateString), 'h:mm a');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-start mb-4">
            <h3 className="text-lg font-semibold text-gray-900">{event.title}</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <FiX className="h-5 w-5" />
            </button>
          </div>

          {event.banner_image_url && (
            <img
              src={event.banner_image_url}
              alt={event.title}
              className="w-full h-32 object-cover rounded-lg mb-4"
            />
          )}

          <div className="space-y-3">
            <div className="flex items-center text-sm text-gray-600">
              <FiCalendar className="h-4 w-4 mr-2" />
              <div>
                <div>{formatDate(event.start_datetime || event.startDateTime)}</div>
                <div>{formatTime(event.start_datetime || event.startDateTime)} - {formatTime(event.end_datetime || event.endDateTime)}</div>
              </div>
            </div>

            {event.location && (
              <div className="flex items-center text-sm text-gray-600">
                <FiMapPin className="h-4 w-4 mr-2" />
                <div>
                  <div>{event.location.name}</div>
                  {event.location.address && <div className="text-xs">{event.location.address}</div>}
                </div>
              </div>
            )}

            <div className="flex items-center text-sm text-gray-600">
              <FiUsers className="h-4 w-4 mr-2" />
              <span>{event.current_attendees || 0} / {event.max_attendees || 'Unlimited'} attendees</span>
            </div>

            {event.description && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                <p className="text-sm text-gray-600">{event.description}</p>
              </div>
            )}

            <div className="flex items-center gap-2">
              {event.is_featured && (
                <span className="inline-flex items-center px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">
                  <FiStar className="h-3 w-3 mr-1" />
                  Featured
                </span>
              )}
              {event.is_competition && (
                <span className="inline-flex items-center px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded">
                  <FiAward className="h-3 w-3 mr-1" />
                  Competition
                </span>
              )}
              <span className={`px-2 py-1 text-xs rounded ${
                event.category === 'workshop' ? 'bg-blue-100 text-blue-800' :
                event.category === 'conference' ? 'bg-purple-100 text-purple-800' :
                event.category === 'webinar' ? 'bg-green-100 text-green-800' :
                event.category === 'competition' ? 'bg-orange-100 text-orange-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {event.category || 'Event'}
              </span>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Close
            </button>
            <button
              onClick={() => {
                // Handle event registration or view details
                console.log('View event details:', event);
              }}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              View Details
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventCalendar;
