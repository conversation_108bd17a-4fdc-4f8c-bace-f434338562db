import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchMentorsList } from '../../store/slices/MentorsSlice';

const MentorAPITest = () => {
  const dispatch = useDispatch();
  const [testResults, setTestResults] = useState(null);
  
  const { 
    publicMentors, 
    publicMentorsLoading, 
    publicMentorsError 
  } = useSelector(state => state.mentors);

  useEffect(() => {
    const testAPI = async () => {
      try {
        console.log('Testing fetchMentorsList...');
        const result = await dispatch(fetchMentorsList({
          verified_only: true,
          skip: 0,
          limit: 5
        }));
        console.log('API Result:', result);
        setTestResults(result);
      } catch (error) {
        console.error('API Error:', error);
        setTestResults({ error: error.message });
      }
    };

    testAPI();
  }, [dispatch]);

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h2 className="text-xl font-bold mb-4">Mentor API Test</h2>
      
      <div className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="font-semibold mb-2">API Status:</h3>
          <p>Loading: {publicMentorsLoading ? 'Yes' : 'No'}</p>
          <p>Error: {publicMentorsError ? JSON.stringify(publicMentorsError, null, 2) : 'None'}</p>
        </div>

        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="font-semibold mb-2">Raw API Response:</h3>
          <pre className="text-xs overflow-auto max-h-40 bg-white p-2 rounded border">
            {JSON.stringify(publicMentors, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="font-semibold mb-2">Test Results:</h3>
          <pre className="text-xs overflow-auto max-h-40 bg-white p-2 rounded border">
            {JSON.stringify(testResults, null, 2)}
          </pre>
        </div>

        {publicMentors && (
          <div className="bg-green-50 p-4 rounded-md">
            <h3 className="font-semibold mb-2 text-green-800">Parsed Mentors:</h3>
            <div className="space-y-2">
              {(publicMentors?.mentors || publicMentors?.data || publicMentors || []).map((mentor, index) => (
                <div key={index} className="bg-white p-2 rounded border text-sm">
                  <p><strong>ID:</strong> {mentor?.id || 'N/A'}</p>
                  <p><strong>Name:</strong> {mentor?.full_name || mentor?.name || 'N/A'}</p>
                  <p><strong>Rating:</strong> {mentor?.rating || 'N/A'}</p>
                  <p><strong>Experience:</strong> {mentor?.experience_years || 0} years</p>
                  <p><strong>Verified:</strong> {mentor?.is_verified ? 'Yes' : 'No'}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MentorAPITest;
