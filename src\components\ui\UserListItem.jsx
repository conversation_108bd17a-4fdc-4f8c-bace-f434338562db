import React, { useState, useRef, useEffect } from 'react';
import { FiMoreVertical, FiUser, FiUserPlus, FiMail, FiPhone, FiMapPin } from 'react-icons/fi';

/**
 * Reusable UserListItem component for displaying users consistently across the app
 * 
 * @param {Object} user - User object with id, username, email, profile_picture, etc.
 * @param {Function} onFollow - Callback when follow is clicked
 * @param {Function} onVisitProfile - Callback when visit profile is clicked
 * @param {Array} customActions - Additional custom actions for the dropdown
 * @param {boolean} showEmail - Whether to show email (default: true)
 * @param {boolean} showPhone - Whether to show phone number (default: false)
 * @param {boolean} showLocation - Whether to show location/country (default: false)
 * @param {string} size - Size variant: 'sm', 'md', 'lg' (default: 'md')
 * @param {string} variant - Style variant: 'default', 'compact', 'detailed' (default: 'default')
 * @param {boolean} showActions - Whether to show the actions dropdown (default: true)
 * @param {string} className - Additional CSS classes
 */
const UserListItem = ({
  user,
  onFollow,
  onVisitProfile,
  customActions = [],
  showEmail = true,
  showPhone = false,
  showLocation = false,
  size = 'md',
  variant = 'default',
  showActions = true,
  className = ''
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Size configurations
  const sizeConfig = {
    sm: {
      avatar: 'w-8 h-8',
      text: 'text-sm',
      subtext: 'text-xs',
      padding: 'p-3'
    },
    md: {
      avatar: 'w-10 h-10',
      text: 'text-sm',
      subtext: 'text-xs',
      padding: 'p-4'
    },
    lg: {
      avatar: 'w-12 h-12',
      text: 'text-base',
      subtext: 'text-sm',
      padding: 'p-6'
    }
  };

  const config = sizeConfig[size];

  // Generate user initials for avatar fallback
  const getInitials = (name) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Handle action clicks
  const handleActionClick = (action) => {
    setShowDropdown(false);
    if (action.onClick) {
      action.onClick(user);
    }
  };

  // Default actions
  const defaultActions = [
    {
      id: 'follow',
      label: 'Follow',
      icon: FiUserPlus,
      onClick: onFollow
    },
    {
      id: 'profile',
      label: 'Visit Profile',
      icon: FiUser,
      onClick: onVisitProfile
    }
  ];

  // Combine default and custom actions
  const allActions = [...defaultActions, ...customActions].filter(action => action.onClick);

  return (
    <div className={`flex items-center justify-between ${config.padding} hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${className}`}>
      {/* User Info */}
      <div className="flex items-center space-x-3 min-w-0 flex-1">
        {/* Avatar */}
        <div className={`${config.avatar} rounded-full flex-shrink-0 overflow-hidden`}>
          {(() => {
            // Helper function to get full image URL
            const getFullImageUrl = (imageUrl) => {
              if (!imageUrl) return null;

              // If it's base64 data, return as is
              if (imageUrl.startsWith('data:')) {
                return imageUrl;
              }

              // If it's already a full URL, return as is
              if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
                return imageUrl;
              }

              // If it's a relative path, prepend the API base URL
              if (imageUrl.startsWith('/')) {
                const API_BASE = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';
                return `${API_BASE}${imageUrl}`;
              }

              // If it's just a filename, assume it's in the static folder
              const API_BASE = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';
              return `${API_BASE}/static/${imageUrl}`;
            };

            const rawImageUrl = user.profile_picture_thumbnail_url ||
                               user.profile_picture_url ||
                               user.profile_picture ||
                               user.profilePicture ||
                               user.profile_image_url;

            const imageUrl = getFullImageUrl(rawImageUrl);

            return imageUrl ? (
              <img
                src={imageUrl}
                alt={user.username || user.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
            ) : null;
          })()}
          <div
            className={`${config.avatar} bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center ${user.profile_picture || user.profilePicture ? 'hidden' : 'flex'}`}
          >
            <span className={`${config.text} font-semibold text-white`}>
              {getInitials(user.username || user.name)}
            </span>
          </div>
        </div>

        {/* User Details */}
        <div className="min-w-0 flex-1">
          {/* Name */}
          <div className="flex items-center space-x-2">
            <h4 className={`${config.text} font-medium text-gray-900 dark:text-white truncate`}>
              {user.username || user.name || 'Unknown User'}
            </h4>

            {/* Verification badge */}
            {user.is_email_verified && (
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Verified
              </span>
            )}
          </div>

          {/* Email */}
          {showEmail && user.email && (
            <p className={`${config.subtext} text-gray-600 dark:text-gray-400 truncate mt-1`}>
              {user.email}
            </p>
          )}
        </div>
      </div>

      {/* Actions Dropdown */}
      {showActions && allActions.length > 0 && (
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          >
            <FiMoreVertical className="w-4 h-4" />
          </button>

          {/* Dropdown Menu */}
          {showDropdown && (
            <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50">
              {allActions.map((action) => {
                const Icon = action.icon;
                return (
                  <button
                    key={action.id}
                    onClick={() => handleActionClick(action)}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left"
                  >
                    {Icon && <Icon className="w-4 h-4" />}
                    <span>{action.label}</span>
                  </button>
                );
              })}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default UserListItem;
