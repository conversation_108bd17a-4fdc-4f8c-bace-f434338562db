import React from 'react';
import { 
  FiStar, 
  FiMapPin, 
  FiClock, 
  FiDollarSign,
  FiAward,
  FiCheckCircle,
  FiMessageCircle,
  FiExternalLink,
  FiGlobe
} from 'react-icons/fi';

const MentorCard = ({
  mentor,
  onViewDetails,
  onContact,
  showActions = true,
  variant = 'default' // 'default', 'compact', 'detailed'
}) => {
  // Helper function to get full image URL
  const getFullImageUrl = (imageUrl) => {
    if (!imageUrl) return null;

    // If it's base64 data, return as is
    if (imageUrl.startsWith('data:')) {
      return imageUrl;
    }

    // If it's already a full URL, return as is
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // If it's a relative path, prepend the API base URL
    if (imageUrl.startsWith('/')) {
      const API_BASE = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';
      return `${API_BASE}${imageUrl}`;
    }

    // If it's just a filename, assume it's in the static folder
    const API_BASE = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';
    return `${API_BASE}/static/${imageUrl}`;
  };

  // Handle the new API response structure
  const userData = mentor.user || mentor;
  const profileData = mentor.profile || mentor.user?.mentor_profile || mentor;

  const {
    id = userData.id,
    username = userData.username,
    full_name = userData.full_name || userData.username,
    bio = profileData.bio,
    expertise_areas = profileData.expertise_subjects || mentor.expertise_areas || [],
    experience_years = profileData.experience_years || 0,
    hourly_rate = profileData.hourly_rate,
    rating = mentor.average_rating || profileData.average_rating || 0,
    total_reviews = mentor.total_reviews || profileData.total_reviews || 0,
    is_verified = mentor.verification_status === 'verified' || profileData.verification_status === 'verified' || mentor.is_verified,
    is_available = mentor.is_available !== undefined ? mentor.is_available : true,
    country = userData.country,
    profile_image_url = getFullImageUrl(profileData.profile_image_url || userData.profile_picture),
    preferred_subjects = profileData.preferred_subjects || mentor.preferred_subjects || [],
    languages = profileData.languages || mentor.languages || [],
    total_students = mentor.total_students || profileData.total_students || 0,
    total_sessions = mentor.total_sessions || profileData.total_sessions || 0,
    response_time = mentor.response_time || profileData.response_time || 'within 24 hours',
    last_active = mentor.last_active || profileData.last_active
  } = {
    ...userData,
    ...profileData,
    ...mentor
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <FiStar key={i} className="h-4 w-4 text-yellow-400 fill-current" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <FiStar key="half" className="h-4 w-4 text-yellow-400 fill-current opacity-50" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <FiStar key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
      );
    }

    return stars;
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(mentor);
    }
  };

  const handleContact = (e) => {
    e.stopPropagation();
    if (onContact) {
      onContact(mentor);
    }
  };

  if (variant === 'compact') {
    return (
      <div 
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
        onClick={handleViewDetails}
      >
        <div className="flex items-center space-x-3">
          <div className="relative">
            <img
              src={profile_image_url || '/default-avatar.png'}
              alt={full_name}
              className="w-12 h-12 rounded-full object-cover"
            />
            {is_verified && (
              <div className="absolute -top-1 -right-1">
                <FiCheckCircle className="h-5 w-5 text-green-500 bg-white rounded-full" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 truncate">{full_name}</h3>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <div className="flex items-center">
                {renderStars(rating).slice(0, 5)}
                <span className="ml-1">({total_reviews})</span>
              </div>
              <span>•</span>
              <span>${hourly_rate}/hr</span>
            </div>
            <div className="flex items-center space-x-1 mt-1">
              {expertise_areas.slice(0, 2).map((area, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {typeof area === 'object' ? area.name : area}
                </span>
              ))}
              {expertise_areas.length > 2 && (
                <span className="text-xs text-gray-500">+{expertise_areas.length - 2}</span>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
      onClick={handleViewDetails}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start space-x-4 mb-4">
          <div className="relative">
            <img
              src={profile_image_url || '/default-avatar.png'}
              alt={full_name}
              className="w-16 h-16 rounded-full object-cover"
            />
            {is_verified && (
              <div className="absolute -top-1 -right-1">
                <FiCheckCircle className="h-6 w-6 text-green-500 bg-white rounded-full" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">{full_name}</h3>
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                is_available 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-1 ${
                  is_available ? 'bg-green-400' : 'bg-gray-400'
                }`} />
                {is_available ? 'Available' : 'Busy'}
              </div>
            </div>
            
            {/* Rating */}
            <div className="flex items-center space-x-2 mt-1">
              <div className="flex items-center">
                {renderStars(rating)}
              </div>
              <span className="text-sm text-gray-600">
                {rating.toFixed(1)} ({total_reviews} reviews)
              </span>
            </div>

            {/* Location and Experience */}
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
              <div className="flex items-center">
                <FiMapPin className="h-4 w-4 mr-1" />
                {country}
              </div>
              <div className="flex items-center">
                <FiClock className="h-4 w-4 mr-1" />
                {experience_years} years exp.
              </div>
              <div className="flex items-center">
                <FiDollarSign className="h-4 w-4 mr-1" />
                ${hourly_rate}/hr
              </div>
            </div>
          </div>
        </div>

        {/* Bio */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">{bio}</p>

        {/* Additional Stats */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
          <div className="flex items-center space-x-3">
            {total_students > 0 && (
              <span>{total_students} students</span>
            )}
            {total_sessions > 0 && (
              <span>{total_sessions} sessions</span>
            )}
            <span>Responds {response_time}</span>
          </div>
        </div>

        {/* Expertise Areas */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Expertise</h4>
          <div className="flex flex-wrap gap-2">
            {expertise_areas.slice(0, 4).map((area, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                {typeof area === 'object' ? area.name : area}
              </span>
            ))}
            {expertise_areas.length > 4 && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                +{expertise_areas.length - 4} more
              </span>
            )}
          </div>
        </div>

        {/* Languages */}
        {languages && languages.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center text-sm text-gray-500">
              <FiGlobe className="h-4 w-4 mr-1" />
              <span>Languages: {languages.join(', ')}</span>
            </div>
          </div>
        )}

        {/* Verification Badge */}
        {is_verified && (
          <div className="mb-4">
            <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <FiAward className="h-3 w-3 mr-1" />
              Verified Mentor
            </div>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <button
              onClick={handleViewDetails}
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              View Profile
              <FiExternalLink className="h-4 w-4 ml-1" />
            </button>
            
            {is_available && (
              <button
                onClick={handleContact}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiMessageCircle className="h-4 w-4 mr-2" />
                Contact
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MentorCard;
