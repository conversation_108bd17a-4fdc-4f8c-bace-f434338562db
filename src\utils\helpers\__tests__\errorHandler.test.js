import { getErrorMessage } from '../errorHandler';

describe('getErrorMessage', () => {
  test('should return string error as is', () => {
    const error = 'Simple error message';
    expect(getErrorMessage(error)).toBe('Simple error message');
  });

  test('should extract message from error object', () => {
    const error = { message: 'Error message from object' };
    expect(getErrorMessage(error)).toBe('Error message from object');
  });

  test('should extract detail from error object', () => {
    const error = { detail: 'Error detail from object' };
    expect(getErrorMessage(error)).toBe('Error detail from object');
  });

  test('should handle object detail property (the main issue)', () => {
    const error = { detail: { message: 'Nested error message' } };
    const result = getErrorMessage(error);
    expect(result).toBe('{"message":"Nested error message"}');
  });

  test('should handle complex nested error objects', () => {
    const error = { 
      detail: { 
        code: 'VALIDATION_ERROR',
        message: 'Validation failed',
        fields: ['email', 'password']
      } 
    };
    const result = getErrorMessage(error);
    expect(result).toContain('VALIDATION_ERROR');
    expect(result).toContain('Validation failed');
  });

  test('should return fallback for null/undefined', () => {
    expect(getErrorMessage(null)).toBe('An error occurred');
    expect(getErrorMessage(undefined)).toBe('An error occurred');
  });

  test('should return custom fallback', () => {
    expect(getErrorMessage(null, 'Custom fallback')).toBe('Custom fallback');
  });

  test('should handle empty object', () => {
    const error = {};
    const result = getErrorMessage(error);
    expect(result).toBe('{}');
  });

  test('should prefer message over detail', () => {
    const error = { 
      message: 'Primary message',
      detail: 'Secondary detail'
    };
    expect(getErrorMessage(error)).toBe('Primary message');
  });

  test('should handle non-object, non-string values', () => {
    expect(getErrorMessage(123)).toBe('123');
    expect(getErrorMessage(true)).toBe('true');
  });
});
