import { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  updateMentorProfile,
  fetchMentorProfile,
  fetchMentorProfileCached,
  selectIsProfileCacheValid,
  clearProfileCache
} from '../store/slices/MentorsSlice';

/**
 * Custom hook for managing mentor profile operations
 * Provides state management and operations for mentor profile data
 */
export const useMentorProfile = () => {
  const dispatch = useDispatch();
  
  // Redux state
  const {
    myProfile,
    profileUpdateLoading,
    profileUpdateError,
    profileUpdateSuccess,
    profileLoading,
    profileError
  } = useSelector(state => state.mentors);

  // Cache state
  const isCacheValid = useSelector(selectIsProfileCacheValid);

  // Local state
  const [localProfile, setLocalProfile] = useState(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  // Fetch profile on mount (using cached version)
  useEffect(() => {
    dispatch(fetchMentorProfileCached());
  }, [dispatch]);

  // Initialize local profile when Redux profile changes
  useEffect(() => {
    if (myProfile) {
      // Handle the new API response structure
      const profileData = {
        // User information
        id: myProfile.id,
        user_id: myProfile.user_id,
        username: myProfile.username,
        email: myProfile.email,
        mobile: myProfile.mobile,
        country: myProfile.country,
        profile_picture: myProfile.profile_picture,
        user_type: myProfile.user_type,
        is_email_verified: myProfile.is_email_verified,
        is_mobile_verified: myProfile.is_mobile_verified,
        created_at: myProfile.created_at,

        // Mentor profile information
        bio: myProfile.bio,
        experience_years: myProfile.experience_years,
        hourly_rate: myProfile.hourly_rate,
        languages: myProfile.languages || [],
        availability_hours: myProfile.availability_hours || {},
        profile_image_url: myProfile.profile_image_url,
        expertise_subjects: myProfile.expertise_subjects || [],
        preferred_subjects: myProfile.preferred_subjects || [],

        // Additional fields
        total_competitions: myProfile.total_competitions,
        active_institutes: myProfile.active_institutes,
        average_rating: myProfile.average_rating,
        verification_status: myProfile.verification_status,

        // For backward compatibility, also include the IDs arrays
        expertise_subject_ids: myProfile.expertise_subjects?.map(s => s.id) || [],
        preferred_subject_ids: myProfile.preferred_subjects?.map(s => s.id) || []
      };

      setLocalProfile(profileData);
      setHasUnsavedChanges(false);
    }
  }, [myProfile]);

  // Validation rules
  const validateProfile = useCallback((profileData) => {
    const errors = {};

    // Required fields validation
    if (!profileData.bio || profileData.bio.trim().length < 50) {
      errors.bio = 'Bio must be at least 50 characters long';
    }

    if (!profileData.experience_years || profileData.experience_years < 0) {
      errors.experience_years = 'Experience years must be a positive number';
    }

    if (!profileData.hourly_rate || profileData.hourly_rate < 1) {
      errors.hourly_rate = 'Hourly rate must be at least $1';
    }

    // Subject relationships validation
    const expertiseSubjects = profileData.expertise_subject_ids || profileData.expertise_subjects?.map(s => s.id) || [];
    if (expertiseSubjects.length === 0) {
      errors.expertise_subject_ids = 'At least one expertise subject is required';
    }

    const preferredSubjects = profileData.preferred_subject_ids || profileData.preferred_subjects?.map(s => s.id) || [];
    if (preferredSubjects.length === 0) {
      errors.preferred_subject_ids = 'At least one preferred subject is required';
    }

    const languages = profileData.languages?.filter(lang => lang && lang.trim()) || [];
    if (languages.length === 0) {
      errors.languages = 'At least one language is required';
    }

    return errors;
  }, []);

  // Helper function to validate URLs
  const isValidUrl = (string) => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  // Update local profile
  const updateLocalProfile = useCallback((updates) => {
    setLocalProfile(prev => ({ ...prev, ...updates }));
    setHasUnsavedChanges(true);

    // Clear validation errors for updated fields
    const updatedFields = Object.keys(updates);
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      updatedFields.forEach(field => {
        delete newErrors[field];
      });
      return newErrors;
    });
  }, []);

  // Update subject selections
  const updateSubjectSelection = useCallback((fieldName, subjectIds) => {
    setLocalProfile(prev => ({
      ...prev,
      [fieldName]: subjectIds
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Update array field in local profile (for languages)
  const updateArrayField = useCallback((fieldName, index, value) => {
    setLocalProfile(prev => ({
      ...prev,
      [fieldName]: prev[fieldName].map((item, i) => i === index ? value : item)
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Add item to array field (for languages)
  const addArrayItem = useCallback((fieldName, defaultValue = '') => {
    setLocalProfile(prev => ({
      ...prev,
      [fieldName]: [...(prev[fieldName] || []), defaultValue]
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Remove item from array field (for languages)
  const removeArrayItem = useCallback((fieldName, index) => {
    setLocalProfile(prev => ({
      ...prev,
      [fieldName]: prev[fieldName].filter((_, i) => i !== index)
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Update availability
  const updateAvailability = useCallback((day, field, value) => {
    setLocalProfile(prev => ({
      ...prev,
      availability_hours: {
        ...prev.availability_hours,
        [day]: {
          ...prev.availability_hours[day],
          [field]: value
        }
      }
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Save profile
  const saveProfile = useCallback(async () => {
    if (!localProfile) {
      return { success: false, error: 'No profile data to save' };
    }

    // Validate profile
    const errors = validateProfile(localProfile);

    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return { success: false, errors };
    }

    try {
      // Clean up the data before submission
      const cleanedProfile = {};

      // Required fields
      if (localProfile.bio && localProfile.bio.trim()) {
        cleanedProfile.bio = localProfile.bio.trim();
      }

      if (localProfile.experience_years !== undefined && localProfile.experience_years !== null) {
        cleanedProfile.experience_years = parseInt(localProfile.experience_years) || 0;
      }

      if (localProfile.hourly_rate !== undefined && localProfile.hourly_rate !== null) {
        cleanedProfile.hourly_rate = parseFloat(localProfile.hourly_rate) || 0;
      }

      // Subject IDs - handle different possible field names
      const expertiseIds = localProfile.expertise_subject_ids || localProfile.expertise_subjects?.map(s => s.id) || [];
      if (Array.isArray(expertiseIds) && expertiseIds.length > 0) {
        cleanedProfile.expertise_subject_ids = expertiseIds;
      }

      const preferredIds = localProfile.preferred_subject_ids || localProfile.preferred_subjects?.map(s => s.id) || [];
      if (Array.isArray(preferredIds) && preferredIds.length > 0) {
        cleanedProfile.preferred_subject_ids = preferredIds;
      }

      // Optional fields
      if (Array.isArray(localProfile.languages) && localProfile.languages.length > 0) {
        const filteredLanguages = localProfile.languages.filter(lang => lang && lang.trim());
        if (filteredLanguages.length > 0) {
          cleanedProfile.languages = filteredLanguages;
        }
      }

      if (localProfile.availability_hours && typeof localProfile.availability_hours === 'object') {
        cleanedProfile.availability_hours = localProfile.availability_hours;
      }

      // Add profile image URL if present
      if (localProfile.profile_image_url) {
        cleanedProfile.profile_image_url = localProfile.profile_image_url;
      }

      // Update the mentor profile
      await dispatch(updateMentorProfile(cleanedProfile)).unwrap();

      setHasUnsavedChanges(false);
      setValidationErrors({});
      return { success: true };
    } catch (error) {
      console.error('Failed to save profile:', error);

      // Handle different error formats
      let errorMessage = 'Failed to save profile';
      if (error.detail) {
        errorMessage = error.detail;
      } else if (error.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      return { success: false, error: errorMessage };
    }
  }, [localProfile, validateProfile, dispatch]);

  // Calculate profile completion percentage
  const calculateCompletion = useCallback((profileData) => {
    if (!profileData) return 0;

    const requiredFields = [
      'bio',
      'experience_years',
      'hourly_rate',
      'expertise_subjects',
      'preferred_subjects',
      'languages'
    ];

    const optionalFields = [
      'profile_image_url',
      'availability_hours'
    ];

    let completed = 0;
    let total = requiredFields.length + optionalFields.length;

    // Check required fields
    requiredFields.forEach(field => {
      if (field === 'expertise_subjects' || field === 'preferred_subjects') {
        if (profileData[field] && Array.isArray(profileData[field]) && profileData[field].length > 0) {
          completed++;
        }
      } else if (field === 'languages') {
        if (profileData[field] && Array.isArray(profileData[field]) && profileData[field].length > 0) {
          completed++;
        }
      } else if (profileData[field] && String(profileData[field]).trim()) {
        completed++;
      }
    });

    // Check optional fields
    optionalFields.forEach(field => {
      if (field === 'availability_hours') {
        if (profileData[field] && typeof profileData[field] === 'object' && Object.keys(profileData[field]).length > 0) {
          completed++;
        }
      } else if (profileData[field] && String(profileData[field]).trim()) {
        completed++;
      }
    });

    return Math.round((completed / total) * 100);
  }, []);

  // Fetch profile (with caching)
  const fetchProfile = useCallback((forceRefresh = false) => {
    if (forceRefresh) {
      return dispatch(fetchMentorProfile());
    }
    return dispatch(fetchMentorProfileCached());
  }, [dispatch]);

  // Clear profile cache
  const clearCache = useCallback(() => {
    dispatch(clearProfileCache());
  }, [dispatch]);

  // Force refresh profile (bypass cache)
  const forceRefreshProfile = useCallback(() => {
    return dispatch(fetchMentorProfile());
  }, [dispatch]);

  // Reset local changes
  const resetChanges = useCallback(() => {
    if (myProfile) {
      setLocalProfile(myProfile);
      setHasUnsavedChanges(false);
      setValidationErrors({});
    }
  }, [myProfile]);

  // Check if profile is complete
  const isProfileComplete = useCallback((profile = localProfile) => {
    if (!profile) return false;

    const requiredFields = [
      'bio', 'experience_years', 'hourly_rate'
    ];

    const hasRequiredFields = requiredFields.every(field =>
      profile[field] && profile[field].toString().trim()
    );

    // Check for expertise subjects - handle both formats
    const expertiseSubjects = profile.expertise_subjects?.length > 0 ||
                             profile.expertise_subject_ids?.length > 0;

    // Check for preferred subjects - handle both formats
    const preferredSubjects = profile.preferred_subjects?.length > 0 ||
                             profile.preferred_subject_ids?.length > 0;

    const hasLanguages = profile.languages?.some(lang => lang && lang.trim());

    return hasRequiredFields && expertiseSubjects && preferredSubjects && hasLanguages;
  }, [localProfile]);

  return {
    // Profile data
    profile: localProfile,
    originalProfile: myProfile,
    
    // Loading states
    isLoading: profileLoading,
    isSaving: profileUpdateLoading,
    
    // Error states
    error: profileError,
    saveError: profileUpdateError,
    validationErrors,
    
    // Success states
    saveSuccess: profileUpdateSuccess,
    
    // State flags
    hasUnsavedChanges,
    isProfileComplete: isProfileComplete(),
    isCacheValid,

    // Actions
    updateLocalProfile,
    updateSubjectSelection,
    updateArrayField,
    addArrayItem,
    removeArrayItem,
    updateAvailability,
    saveProfile,
    fetchProfile,
    forceRefreshProfile,
    clearCache,
    resetChanges,
    validateProfile: () => validateProfile(localProfile),
    calculateCompletion
  };
};
