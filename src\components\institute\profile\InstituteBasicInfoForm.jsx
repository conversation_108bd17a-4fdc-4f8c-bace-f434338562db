import React from 'react';
import { FiHome, FiUsers, FiCalendar, FiFileText } from 'react-icons/fi';

const InstituteBasicInfoForm = ({
  formData,
  onChange,
  isEditing,
  fieldErrors,
  mandatoryFields,
  hasAttemptedSubmit
}) => {
  const instituteTypes = [
    { value: 'university', label: 'University' },
    { value: 'college', label: 'College' },
    { value: 'school', label: 'School' },
    { value: 'training_center', label: 'Training Center' },
    { value: 'research_institute', label: 'Research Institute' },
    { value: 'vocational_school', label: 'Vocational School' },
    { value: 'online_platform', label: 'Online Platform' },
    { value: 'other', label: 'Other' }
  ];

  const getFieldError = (fieldName) => {
    return hasAttemptedSubmit && fieldErrors[fieldName];
  };

  const isFieldRequired = (fieldName) => {
    return mandatoryFields && mandatoryFields[fieldName];
  };

  const renderField = (fieldName, label, type = 'text', options = null, placeholder = '') => {
    const error = getFieldError(fieldName);
    const required = isFieldRequired(fieldName);
    
    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        {type === 'select' ? (
          <select
            name={fieldName}
            value={formData[fieldName] || ''}
            onChange={onChange}
            disabled={!isEditing}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              !isEditing ? 'bg-gray-50 text-gray-600' : 'bg-white'
            } ${error ? 'border-red-300' : 'border-gray-300'}`}
          >
            <option value="">Select {label}</option>
            {options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : type === 'textarea' ? (
          <textarea
            name={fieldName}
            value={formData[fieldName] || ''}
            onChange={onChange}
            disabled={!isEditing}
            placeholder={placeholder}
            rows={4}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none ${
              !isEditing ? 'bg-gray-50 text-gray-600' : 'bg-white'
            } ${error ? 'border-red-300' : 'border-gray-300'}`}
          />
        ) : type === 'number' ? (
          <input
            type="number"
            name={fieldName}
            value={formData[fieldName] || ''}
            onChange={onChange}
            disabled={!isEditing}
            placeholder={placeholder}
            min={type === 'number' && fieldName === 'established_year' ? 1800 : undefined}
            max={type === 'number' && fieldName === 'established_year' ? new Date().getFullYear() : undefined}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              !isEditing ? 'bg-gray-50 text-gray-600' : 'bg-white'
            } ${error ? 'border-red-300' : 'border-gray-300'}`}
          />
        ) : (
          <input
            type={type}
            name={fieldName}
            value={formData[fieldName] || ''}
            onChange={onChange}
            disabled={!isEditing}
            placeholder={placeholder}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              !isEditing ? 'bg-gray-50 text-gray-600' : 'bg-white'
            } ${error ? 'border-red-300' : 'border-gray-300'}`}
          />
        )}
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center">
          <FiHome className="h-5 w-5 text-blue-600 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Institute Name and Type */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {renderField(
            'institute_name',
            'Institute Name',
            'text',
            null,
            'Enter your institute name'
          )}
          {renderField(
            'institute_type',
            'Institute Type',
            'select',
            instituteTypes
          )}
        </div>

        {/* Description */}
        {renderField(
          'description',
          'Description',
          'textarea',
          null,
          'Describe your institute, its mission, and what makes it unique...'
        )}

        {/* Established Year and Accreditation */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {renderField(
            'established_year',
            'Established Year',
            'number',
            null,
            'e.g., 1990'
          )}
          {renderField(
            'accreditation',
            'Accreditation',
            'text',
            null,
            'Enter accreditation details'
          )}
        </div>

        {/* Address */}
        {renderField(
          'address',
          'Address',
          'text',
          null,
          'Enter complete address'
        )}

        {/* City, State, Postal Code */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {renderField(
            'city',
            'City',
            'text',
            null,
            'Enter city'
          )}
          {renderField(
            'state',
            'State/Province',
            'text',
            null,
            'Enter state or province'
          )}
          {renderField(
            'postal_code',
            'Postal Code',
            'text',
            null,
            'Enter postal code'
          )}
        </div>
      </div>
    </div>
  );
};

export default InstituteBasicInfoForm;
