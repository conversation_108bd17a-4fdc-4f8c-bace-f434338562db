import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import axios from 'axios';

// Base URL for events endpoints
const BASE_URL = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';
const API_BASE = `${BASE_URL}/api/events`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Async Thunks for API calls

// 1. Get Public Events
export const fetchPublicEvents = createAsyncThunk(
  'events/fetchPublicEvents',
  async (queryParams = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      
      // Add query parameters
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const res = await axios.get(`${API_BASE}/public?${params}`);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get Public Event Details
export const fetchEventDetails = createAsyncThunk(
  'events/fetchEventDetails',
  async (eventId, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/public/${eventId}`);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Get Featured Events
export const fetchFeaturedEvents = createAsyncThunk(
  'events/fetchFeaturedEvents',
  async ({ limit = 5 } = {}, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/featured?limit=${limit}`);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Search Events
export const searchEvents = createAsyncThunk(
  'events/searchEvents',
  async ({ q, limit = 20 }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({ q, limit: limit.toString() });
      const res = await axios.get(`${API_BASE}/search?${params}`);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Create Event (Teachers Only)
export const createEvent = createAsyncThunk(
  'events/createEvent',
  async (eventData, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/`, eventData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 6. Get My Events (Teachers Only)
export const fetchMyEvents = createAsyncThunk(
  'events/fetchMyEvents',
  async ({ page = 1, size = 10 } = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({ page: page.toString(), size: size.toString() });
      const res = await axios.get(`${API_BASE}/my-events?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 7. Update Event (Organizer Only)
export const updateEvent = createAsyncThunk(
  'events/updateEvent',
  async ({ eventId, eventData }, { rejectWithValue }) => {
    try {
      const res = await axios.put(`${API_BASE}/${eventId}`, eventData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 8. Register for Event
export const registerForEvent = createAsyncThunk(
  'events/registerForEvent',
  async ({ eventId, registrationData }, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/${eventId}/register`, registrationData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 9. Get My Registrations
export const fetchMyRegistrations = createAsyncThunk(
  'events/fetchMyRegistrations',
  async ({ page = 1, size = 10 } = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({ page: page.toString(), size: size.toString() });
      const res = await axios.get(`${API_BASE}/my-registrations?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 10. Cancel Registration
export const cancelRegistration = createAsyncThunk(
  'events/cancelRegistration',
  async (eventId, { rejectWithValue }) => {
    try {
      const res = await axios.delete(`${API_BASE}/${eventId}/register`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return { eventId, message: res.data.message };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  // Public events
  publicEvents: [],
  publicEventsLoading: false,
  publicEventsError: null,
  publicEventsPagination: {
    total: 0,
    page: 1,
    size: 20,
    total_pages: 1
  },

  // Featured events
  featuredEvents: [],
  featuredEventsLoading: false,
  featuredEventsError: null,

  // Event details
  currentEvent: null,
  eventDetailsLoading: false,
  eventDetailsError: null,

  // Search results
  searchResults: [],
  searchLoading: false,
  searchError: null,
  searchQuery: '',

  // My events (for teachers)
  myEvents: [],
  myEventsLoading: false,
  myEventsError: null,
  myEventsPagination: {
    total: 0,
    page: 1,
    size: 10,
    total_pages: 1
  },

  // My registrations
  myRegistrations: [],
  myRegistrationsLoading: false,
  myRegistrationsError: null,
  myRegistrationsPagination: {
    total: 0,
    page: 1,
    size: 10,
    total_pages: 1
  },

  // Event creation/update
  createLoading: false,
  createError: null,
  createSuccess: false,
  updateLoading: false,
  updateError: null,
  updateSuccess: false,

  // Registration
  registrationLoading: false,
  registrationError: null,
  registrationSuccess: false,

  // UI state
  selectedEvent: null,
  showEventDetails: false,
  filters: {
    category_id: null,
    location_id: null,
    is_featured: null,
    is_competition: null,
    search: ''
  }
};

// Events Slice
const eventsSlice = createSlice({
  name: 'events',
  initialState,
  reducers: {
    // Update filters
    updateFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },

    // Reset filters
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },

    // Select event for details view
    selectEvent: (state, action) => {
      state.selectedEvent = action.payload;
      state.showEventDetails = true;
    },

    // Close event details
    closeEventDetails: (state) => {
      state.selectedEvent = null;
      state.showEventDetails = false;
    },

    // Clear errors
    clearErrors: (state) => {
      state.publicEventsError = null;
      state.featuredEventsError = null;
      state.eventDetailsError = null;
      state.searchError = null;
      state.myEventsError = null;
      state.myRegistrationsError = null;
      state.createError = null;
      state.updateError = null;
      state.registrationError = null;
    },

    // Clear success states
    clearSuccessStates: (state) => {
      state.createSuccess = false;
      state.updateSuccess = false;
      state.registrationSuccess = false;
    },

    // Reset events state
    resetEventsState: (state) => {
      return initialState;
    },

    // Set search query
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Public Events
      .addCase(fetchPublicEvents.pending, (state) => {
        state.publicEventsLoading = true;
        state.publicEventsError = null;
      })
      .addCase(fetchPublicEvents.fulfilled, (state, action) => {
        state.publicEventsLoading = false;
        state.publicEvents = action.payload.events;
        state.publicEventsPagination = {
          total: action.payload.total,
          page: action.payload.page,
          size: action.payload.size,
          total_pages: action.payload.total_pages
        };
      })
      .addCase(fetchPublicEvents.rejected, (state, action) => {
        state.publicEventsLoading = false;
        state.publicEventsError = action.payload;
      })

      // Fetch Event Details
      .addCase(fetchEventDetails.pending, (state) => {
        state.eventDetailsLoading = true;
        state.eventDetailsError = null;
      })
      .addCase(fetchEventDetails.fulfilled, (state, action) => {
        state.eventDetailsLoading = false;
        state.currentEvent = action.payload;
      })
      .addCase(fetchEventDetails.rejected, (state, action) => {
        state.eventDetailsLoading = false;
        state.eventDetailsError = action.payload;
      })

      // Fetch Featured Events
      .addCase(fetchFeaturedEvents.pending, (state) => {
        state.featuredEventsLoading = true;
        state.featuredEventsError = null;
      })
      .addCase(fetchFeaturedEvents.fulfilled, (state, action) => {
        state.featuredEventsLoading = false;
        state.featuredEvents = action.payload;
      })
      .addCase(fetchFeaturedEvents.rejected, (state, action) => {
        state.featuredEventsLoading = false;
        state.featuredEventsError = action.payload;
      })

      // Search Events
      .addCase(searchEvents.pending, (state) => {
        state.searchLoading = true;
        state.searchError = null;
      })
      .addCase(searchEvents.fulfilled, (state, action) => {
        state.searchLoading = false;
        state.searchResults = action.payload;
      })
      .addCase(searchEvents.rejected, (state, action) => {
        state.searchLoading = false;
        state.searchError = action.payload;
      })

      // Create Event
      .addCase(createEvent.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
        state.createSuccess = false;
      })
      .addCase(createEvent.fulfilled, (state, action) => {
        state.createLoading = false;
        state.createSuccess = true;
        // Add the new event to myEvents if it exists
        if (state.myEvents) {
          state.myEvents.unshift(action.payload);
        }
      })
      .addCase(createEvent.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Fetch My Events
      .addCase(fetchMyEvents.pending, (state) => {
        state.myEventsLoading = true;
        state.myEventsError = null;
      })
      .addCase(fetchMyEvents.fulfilled, (state, action) => {
        state.myEventsLoading = false;
        state.myEvents = action.payload.events;
        state.myEventsPagination = {
          total: action.payload.total,
          page: action.payload.page,
          size: action.payload.size,
          total_pages: action.payload.total_pages
        };
      })
      .addCase(fetchMyEvents.rejected, (state, action) => {
        state.myEventsLoading = false;
        state.myEventsError = action.payload;
      })

      // Update Event
      .addCase(updateEvent.pending, (state) => {
        state.updateLoading = true;
        state.updateError = null;
        state.updateSuccess = false;
      })
      .addCase(updateEvent.fulfilled, (state, action) => {
        state.updateLoading = false;
        state.updateSuccess = true;
        // Update the event in myEvents if it exists
        const index = state.myEvents.findIndex(event => event.id === action.payload.id);
        if (index !== -1) {
          state.myEvents[index] = action.payload;
        }
        // Update currentEvent if it's the same event
        if (state.currentEvent?.id === action.payload.id) {
          state.currentEvent = action.payload;
        }
      })
      .addCase(updateEvent.rejected, (state, action) => {
        state.updateLoading = false;
        state.updateError = action.payload;
      })

      // Register for Event
      .addCase(registerForEvent.pending, (state) => {
        state.registrationLoading = true;
        state.registrationError = null;
        state.registrationSuccess = false;
      })
      .addCase(registerForEvent.fulfilled, (state, action) => {
        state.registrationLoading = false;
        state.registrationSuccess = true;
        // Add to myRegistrations if it exists
        if (state.myRegistrations) {
          state.myRegistrations.unshift(action.payload);
        }
      })
      .addCase(registerForEvent.rejected, (state, action) => {
        state.registrationLoading = false;
        state.registrationError = action.payload;
      })

      // Fetch My Registrations
      .addCase(fetchMyRegistrations.pending, (state) => {
        state.myRegistrationsLoading = true;
        state.myRegistrationsError = null;
      })
      .addCase(fetchMyRegistrations.fulfilled, (state, action) => {
        state.myRegistrationsLoading = false;
        state.myRegistrations = action.payload.registrations;
        state.myRegistrationsPagination = {
          total: action.payload.total,
          page: action.payload.page,
          size: action.payload.size,
          total_pages: action.payload.total_pages
        };
      })
      .addCase(fetchMyRegistrations.rejected, (state, action) => {
        state.myRegistrationsLoading = false;
        state.myRegistrationsError = action.payload;
      })

      // Cancel Registration
      .addCase(cancelRegistration.fulfilled, (state, action) => {
        // Remove from myRegistrations
        state.myRegistrations = state.myRegistrations.filter(
          registration => registration.event_id !== action.payload.eventId
        );
      });
  }
});

// Actions
export const {
  updateFilters,
  resetFilters,
  selectEvent,
  closeEventDetails,
  clearErrors,
  clearSuccessStates,
  resetEventsState,
  setSearchQuery
} = eventsSlice.actions;

// Selectors
export const selectPublicEvents = (state) => state.events.publicEvents;
export const selectPublicEventsLoading = (state) => state.events.publicEventsLoading;
export const selectPublicEventsError = (state) => state.events.publicEventsError;
export const selectPublicEventsPagination = (state) => state.events.publicEventsPagination;

export const selectFeaturedEvents = (state) => state.events.featuredEvents;
export const selectFeaturedEventsLoading = (state) => state.events.featuredEventsLoading;
export const selectFeaturedEventsError = (state) => state.events.featuredEventsError;

export const selectCurrentEvent = (state) => state.events.currentEvent;
export const selectEventDetailsLoading = (state) => state.events.eventDetailsLoading;
export const selectEventDetailsError = (state) => state.events.eventDetailsError;

export const selectSearchResults = (state) => state.events.searchResults;
export const selectSearchLoading = (state) => state.events.searchLoading;
export const selectSearchError = (state) => state.events.searchError;
export const selectSearchQuery = (state) => state.events.searchQuery;

export const selectMyEvents = (state) => state.events.myEvents;
export const selectMyEventsLoading = (state) => state.events.myEventsLoading;
export const selectMyEventsError = (state) => state.events.myEventsError;
export const selectMyEventsPagination = (state) => state.events.myEventsPagination;

export const selectMyRegistrations = (state) => state.events.myRegistrations;
export const selectMyRegistrationsLoading = (state) => state.events.myRegistrationsLoading;
export const selectMyRegistrationsError = (state) => state.events.myRegistrationsError;
export const selectMyRegistrationsPagination = (state) => state.events.myRegistrationsPagination;

export const selectCreateLoading = (state) => state.events.createLoading;
export const selectCreateError = (state) => state.events.createError;
export const selectCreateSuccess = (state) => state.events.createSuccess;

export const selectUpdateLoading = (state) => state.events.updateLoading;
export const selectUpdateError = (state) => state.events.updateError;
export const selectUpdateSuccess = (state) => state.events.updateSuccess;

export const selectRegistrationLoading = (state) => state.events.registrationLoading;
export const selectRegistrationError = (state) => state.events.registrationError;
export const selectRegistrationSuccess = (state) => state.events.registrationSuccess;

export const selectSelectedEvent = (state) => state.events.selectedEvent;
export const selectShowEventDetails = (state) => state.events.showEventDetails;
export const selectFilters = (state) => state.events.filters;

// Memoized helper selectors
export const selectUpcomingEvents = createSelector(
  [selectPublicEvents],
  (events) => {
    const now = new Date();
    return events.filter(event => new Date(event.start_datetime) > now);
  }
);

export const selectEventsByCategory = (categoryId) => createSelector(
  [selectPublicEvents],
  (events) => {
    return events.filter(event => event.category?.id === categoryId);
  }
);

export const selectCompetitionEvents = createSelector(
  [selectPublicEvents],
  (events) => {
    return events.filter(event => event.is_competition);
  }
);

export const selectIsRegisteredForEvent = (eventId) => (state) => {
  const registrations = state.events.myRegistrations;
  return registrations.some(registration => registration.event_id === eventId);
};

export default eventsSlice.reducer;
