import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  FiAward,
  FiFilter,
  FiPlus,
  FiCalendar,
  FiUsers,
  FiClock
} from 'react-icons/fi';
import {
  fetchCompetitions,
  updateFilters,
  resetFilters,
  selectCompetitions,
  selectCompetitionsLoading,
  selectCompetitionsError,
  selectFilters,
  selectUpcomingCompetitions,
  selectOngoingCompetitions,
  selectCompletedCompetitions
} from '../../store/slices/CompetitionsSlice';
import CompetitionCard from '../../components/competitions/CompetitionCard';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { getUserRole } from '../../utils/helpers/authHelpers';
import { RoleBasedContent } from '../../components/auth/RoleGuard';

const CompetitionsPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'upcoming', 'ongoing', 'completed'

  // Get user role for access control
  const userRole = getUserRole();

  // Redux state
  const competitions = useSelector(selectCompetitions);
  const loading = useSelector(selectCompetitionsLoading);
  const error = useSelector(selectCompetitionsError);
  const filters = useSelector(selectFilters);
  const upcomingCompetitions = useSelector(selectUpcomingCompetitions);
  const ongoingCompetitions = useSelector(selectOngoingCompetitions);
  const completedCompetitions = useSelector(selectCompletedCompetitions);

  // Load initial data
  useEffect(() => {
    dispatch(fetchCompetitions());
  }, [dispatch]);

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    dispatch(updateFilters(newFilters));
    dispatch(fetchCompetitions(newFilters));
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    let filterParams = { ...filters };
    
    switch (tab) {
      case 'upcoming':
        filterParams.status = 'upcoming';
        break;
      case 'ongoing':
        filterParams.status = 'ongoing';
        break;
      case 'completed':
        filterParams.status = 'completed';
        break;
      default:
        filterParams.status = null;
    }
    
    dispatch(updateFilters(filterParams));
    dispatch(fetchCompetitions(filterParams));
  };

  // Clear filters
  const handleClearFilters = () => {
    setActiveTab('all');
    dispatch(resetFilters());
    dispatch(fetchCompetitions());
  };

  const handleViewCompetitionDetails = (competition) => {
    // Navigate to competition details page
    console.log('View competition details:', competition);
  };

  const handleJoinCompetition = (competition) => {
    // Handle competition joining
    console.log('Join competition:', competition);
  };

  const handleStartCompetition = (competition) => {
    // Handle starting competition
    console.log('Start competition:', competition);
  };

  const handleCreateCompetition = () => {
    // Only institutions can create competitions
    if (userRole === 'institute') {
      navigate('/institute/events/create');
    } else {
      alert('Only institutions can create competitions. Teachers and mentors can apply for mentorship roles.');
    }
  };

  const getDisplayCompetitions = () => {
    switch (activeTab) {
      case 'upcoming':
        return upcomingCompetitions;
      case 'ongoing':
        return ongoingCompetitions;
      case 'completed':
        return completedCompetitions;
      default:
        return competitions;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Competitions</h1>
            <p className="mt-2 text-gray-600">
              {userRole === 'institute'
                ? 'Create and manage educational competitions for your institution'
                : 'Participate in educational competitions and showcase your skills'
              }
            </p>
          </div>
          <RoleBasedContent allowedRoles={['institute']}>
            <button
              onClick={handleCreateCompetition}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FiPlus className="h-4 w-4 mr-2" />
              Create Competition
            </button>
          </RoleBasedContent>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiAward className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-lg font-semibold text-gray-900">{competitions.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiCalendar className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Upcoming</p>
              <p className="text-lg font-semibold text-gray-900">{upcomingCompetitions.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiClock className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Ongoing</p>
              <p className="text-lg font-semibold text-gray-900">{ongoingCompetitions.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiAward className="h-8 w-8 text-purple-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-lg font-semibold text-gray-900">{completedCompetitions.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiFilter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={filters.category_id || ''}
                  onChange={(e) => handleFilterChange('category_id', e.target.value || null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  <option value="mathematics">Mathematics</option>
                  <option value="science">Science</option>
                  <option value="programming">Programming</option>
                  <option value="general">General Knowledge</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.status || ''}
                  onChange={(e) => handleFilterChange('status', e.target.value || null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Status</option>
                  <option value="upcoming">Upcoming</option>
                  <option value="ongoing">Ongoing</option>
                  <option value="completed">Completed</option>
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={handleClearFilters}
                  className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'all', name: 'All Competitions', icon: FiAward },
              { id: 'upcoming', name: 'Upcoming', icon: FiCalendar },
              { id: 'ongoing', name: 'Ongoing', icon: FiClock },
              { id: 'completed', name: 'Completed', icon: FiAward }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6">
          <ErrorMessage message={error} />
        </div>
      )}

      {/* Competitions Grid */}
      {loading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {getDisplayCompetitions().map((competition) => (
            <CompetitionCard
              key={competition.id}
              competition={competition}
              onViewDetails={handleViewCompetitionDetails}
              onJoin={handleJoinCompetition}
              onStart={handleStartCompetition}
              isParticipant={false} // This should be determined based on user's participation
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && getDisplayCompetitions().length === 0 && (
        <div className="text-center py-12">
          <FiAward className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No competitions found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {activeTab === 'all' 
              ? 'There are no competitions available at the moment.'
              : `There are no ${activeTab} competitions.`
            }
          </p>
          {activeTab === 'all' && (
            <div className="mt-6">
              <button
                onClick={handleCreateCompetition}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiPlus className="h-4 w-4 mr-2" />
                Create First Competition
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CompetitionsPage;
