import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  FiSearch,
  FiFilter,
  FiStar,
  FiMapPin,
  FiDollarSign,
  FiClock,
  FiUserPlus,
  FiLock
} from 'react-icons/fi';
import {
  fetchMentorsList,
  updateSearchFilters,
  resetSearchFilters,
  selectPublicMentors,
  selectPublicMentorsLoading,
  selectSearchFilters
} from '../../store/slices/MentorsSlice';
import MentorCard from '../../components/mentors/MentorCard';
import { LoadingSpinner } from '../../components/ui';

const MentorsPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Redux state
  const mentors = useSelector(selectPublicMentors);
  const loading = useSelector(selectPublicMentorsLoading);
  const filters = useSelector(selectSearchFilters);

  // Check authentication
  const { user, isAuthenticated } = useSelector(state => state.auth);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', {
        state: {
          from: '/mentors',
          message: 'Please log in to view mentors'
        }
      });
    }
  }, [isAuthenticated, navigate]);

  // Load initial data
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchMentorsList(filters));
    }
  }, [dispatch, isAuthenticated]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    const newFilters = { ...filters, search: searchQuery, page: 1 };
    dispatch(updateSearchFilters(newFilters));
    dispatch(fetchMentorsList(newFilters));
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value, page: 1 };
    dispatch(updateSearchFilters(newFilters));
    dispatch(fetchMentorsList(newFilters));
  };

  // Clear filters
  const handleClearFilters = () => {
    setSearchQuery('');
    dispatch(resetSearchFilters());
    dispatch(fetchMentorsList());
  };

  const handleViewMentorDetails = (mentor) => {
    // Navigate to mentor details page
    console.log('View mentor details:', mentor);
  };

  const handleContactMentor = (mentor) => {
    // Handle mentor contact
    console.log('Contact mentor:', mentor);
  };

  const expertiseOptions = [
    'Mathematics',
    'Physics',
    'Computer Science',
    'Chemistry',
    'Biology',
    'English',
    'History',
    'Economics',
    'Psychology',
    'Engineering'
  ];

  // Show loading while checking authentication
  if (!isAuthenticated) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <FiLock className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Authentication Required</h3>
          <p className="mt-1 text-sm text-gray-500">
            Please log in to view mentors.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Find Mentors</h1>
            <p className="mt-2 text-gray-600">
              Connect with experienced educators and industry professionals
            </p>
          </div>
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <FiUserPlus className="h-4 w-4 mr-2" />
            Become a Mentor
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search mentors by name or expertise..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </form>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiFilter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
              {/* Expertise Areas */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expertise
                </label>
                <select
                  value={filters.expertise_areas[0] || ''}
                  onChange={(e) => handleFilterChange('expertise_areas', e.target.value ? [e.target.value] : [])}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Areas</option>
                  {expertiseOptions.map((area) => (
                    <option key={area} value={area}>{area}</option>
                  ))}
                </select>
              </div>

              {/* Experience Years */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Min Experience
                </label>
                <select
                  value={filters.experience_years_min || ''}
                  onChange={(e) => handleFilterChange('experience_years_min', e.target.value ? parseInt(e.target.value) : null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Any</option>
                  <option value="1">1+ years</option>
                  <option value="3">3+ years</option>
                  <option value="5">5+ years</option>
                  <option value="10">10+ years</option>
                </select>
              </div>

              {/* Hourly Rate */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Rate ($/hr)
                </label>
                <select
                  value={filters.hourly_rate_max || ''}
                  onChange={(e) => handleFilterChange('hourly_rate_max', e.target.value ? parseFloat(e.target.value) : null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Any</option>
                  <option value="25">Under $25</option>
                  <option value="50">Under $50</option>
                  <option value="75">Under $75</option>
                  <option value="100">Under $100</option>
                </select>
              </div>

              {/* Rating */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Min Rating
                </label>
                <select
                  value={filters.rating_min || ''}
                  onChange={(e) => handleFilterChange('rating_min', e.target.value ? parseFloat(e.target.value) : null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Any</option>
                  <option value="3">3+ stars</option>
                  <option value="4">4+ stars</option>
                  <option value="4.5">4.5+ stars</option>
                </select>
              </div>

              {/* Country */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Country
                </label>
                <select
                  value={filters.country || ''}
                  onChange={(e) => handleFilterChange('country', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Countries</option>
                  <option value="USA">United States</option>
                  <option value="Canada">Canada</option>
                  <option value="UK">United Kingdom</option>
                  <option value="Australia">Australia</option>
                  <option value="Germany">Germany</option>
                  <option value="France">France</option>
                  <option value="India">India</option>
                  <option value="China">China</option>
                  <option value="Japan">Japan</option>
                  <option value="Brazil">Brazil</option>
                </select>
              </div>
            </div>

            <div className="mt-4 flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="verified-only"
                  type="checkbox"
                  checked={filters.verified_only}
                  onChange={(e) => handleFilterChange('verified_only', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="verified-only" className="ml-2 block text-sm text-gray-700">
                  Verified mentors only
                </label>
              </div>
              
              <button
                onClick={handleClearFilters}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Clear Filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiStar className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Top Rated</p>
              <p className="text-lg font-semibold text-gray-900">
                {mentors.filter(m => m.rating >= 4.5).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiMapPin className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Available</p>
              <p className="text-lg font-semibold text-gray-900">
                {mentors.filter(m => m.is_available).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiClock className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Experienced</p>
              <p className="text-lg font-semibold text-gray-900">
                {mentors.filter(m => m.experience_years >= 5).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiDollarSign className="h-8 w-8 text-purple-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Avg Rate</p>
              <p className="text-lg font-semibold text-gray-900">
                ${mentors.length > 0 ? Math.round(mentors.reduce((sum, m) => sum + m.hourly_rate, 0) / mentors.length) : 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Mentors Grid */}
      {loading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mentors.map((mentor) => (
            <MentorCard
              key={mentor.id}
              mentor={mentor}
              onViewDetails={handleViewMentorDetails}
              onContact={handleContactMentor}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && mentors.length === 0 && (
        <div className="text-center py-12">
          <FiUserPlus className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No mentors found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filters to find mentors.
          </p>
        </div>
      )}
    </div>
  );
};

export default MentorsPage;
