/**
 * Centralized error handling utility for the EduFair application
 * Provides consistent error handling across API calls and components
 */

import logger from './logger';

// Error types
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  VALIDATION: 'VAL<PERSON>ATION_ERROR',
  AUTHENTICATION: 'AUTHENTICATION_ERROR',
  AUTHORIZATION: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND_ERROR',
  SERVER: 'SERVER_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR',
};

// User-friendly error messages
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK]: 'Unable to connect to the server. Please check your internet connection.',
  [ERROR_TYPES.VALIDATION]: 'Please check your input and try again.',
  [ERROR_TYPES.AUTHENTICATION]: 'Please log in to continue.',
  [ERROR_TYPES.AUTHORIZATION]: 'You do not have permission to perform this action.',
  [ERROR_TYPES.NOT_FOUND]: 'The requested resource was not found.',
  [ERROR_TYPES.SERVER]: 'A server error occurred. Please try again later.',
  [ERROR_TYPES.UNKNOWN]: 'An unexpected error occurred. Please try again.',
};

/**
 * Determine error type based on error object
 */
const getErrorType = (error) => {
  if (!error.response) {
    return ERROR_TYPES.NETWORK;
  }

  const status = error.response.status;
  
  switch (status) {
    case 400:
      return ERROR_TYPES.VALIDATION;
    case 401:
      return ERROR_TYPES.AUTHENTICATION;
    case 403:
      return ERROR_TYPES.AUTHORIZATION;
    case 404:
      return ERROR_TYPES.NOT_FOUND;
    case 500:
    case 502:
    case 503:
    case 504:
      return ERROR_TYPES.SERVER;
    default:
      return ERROR_TYPES.UNKNOWN;
  }
};

/**
 * Extract user-friendly message from error
 */
const extractMessage = (error, fallbackMessage) => {
  // Try to get message from response data
  if (error.response?.data) {
    const data = error.response.data;
    
    // Common API response formats
    if (data.message) return data.message;
    if (data.detail) return data.detail;
    if (data.error) return data.error;
    if (data.errors && Array.isArray(data.errors)) {
      return data.errors.join(', ');
    }
    if (typeof data === 'string') return data;
  }

  // Try to get message from error object
  if (error.message) return error.message;

  // Use fallback message
  return fallbackMessage;
};

/**
 * Main error handling function
 */
export const handleApiError = (error, context = '', customMessage = null) => {
  const errorType = getErrorType(error);
  const defaultMessage = ERROR_MESSAGES[errorType];
  const userMessage = customMessage || extractMessage(error, defaultMessage);
  
  // Log the error with context
  logger.error(
    `API Error in ${context}`,
    {
      type: errorType,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method,
      data: error.response?.data,
      message: error.message,
    },
    context
  );

  return {
    type: errorType,
    message: userMessage,
    status: error.response?.status,
    code: error.code,
    originalError: error,
  };
};

/**
 * Handle form validation errors
 */
export const handleValidationError = (error, context = '') => {
  const validationErrors = {};
  
  if (error.response?.data?.errors) {
    const errors = error.response.data.errors;
    
    // Handle different validation error formats
    if (Array.isArray(errors)) {
      errors.forEach((err) => {
        if (err.field && err.message) {
          validationErrors[err.field] = err.message;
        }
      });
    } else if (typeof errors === 'object') {
      Object.keys(errors).forEach((field) => {
        validationErrors[field] = Array.isArray(errors[field]) 
          ? errors[field].join(', ')
          : errors[field];
      });
    }
  }

  logger.warn(`Validation error in ${context}`, validationErrors, context);

  return {
    type: ERROR_TYPES.VALIDATION,
    message: 'Please correct the highlighted fields.',
    fieldErrors: validationErrors,
    originalError: error,
  };
};

/**
 * Handle async operation errors with retry logic
 */
export const withErrorHandling = (asyncFn, context = '', maxRetries = 0) => {
  return async (...args) => {
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await asyncFn(...args);
      } catch (error) {
        lastError = error;
        
        // Don't retry on client errors (4xx)
        if (error.response?.status >= 400 && error.response?.status < 500) {
          break;
        }
        
        // Don't retry on last attempt
        if (attempt === maxRetries) {
          break;
        }
        
        // Wait before retry (exponential backoff)
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        
        logger.warn(`Retrying ${context} (attempt ${attempt + 2}/${maxRetries + 1})`, null, context);
      }
    }
    
    throw handleApiError(lastError, context);
  };
};

/**
 * Error boundary helper for React components
 */
export const createErrorBoundaryHandler = (componentName) => {
  return (error, errorInfo) => {
    logger.error(
      `React Error Boundary caught error in ${componentName}`,
      {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
      },
      componentName
    );

    // In production, you might want to send this to an error reporting service
    if (import.meta.env.MODE === 'production') {
      // Example: Sentry.captureException(error, { extra: errorInfo });
    }
  };
};

/**
 * Toast notification helper for errors
 */
export const showErrorToast = (error, toast) => {
  const errorData = typeof error === 'string' 
    ? { message: error, type: ERROR_TYPES.UNKNOWN }
    : error;

  const title = getErrorTitle(errorData.type);
  const message = errorData.message || ERROR_MESSAGES[ERROR_TYPES.UNKNOWN];

  if (toast) {
    toast.error(title, message);
  }
};

/**
 * Get appropriate error title based on error type
 */
const getErrorTitle = (errorType) => {
  switch (errorType) {
    case ERROR_TYPES.NETWORK:
      return 'Connection Error';
    case ERROR_TYPES.VALIDATION:
      return 'Validation Error';
    case ERROR_TYPES.AUTHENTICATION:
      return 'Authentication Required';
    case ERROR_TYPES.AUTHORIZATION:
      return 'Access Denied';
    case ERROR_TYPES.NOT_FOUND:
      return 'Not Found';
    case ERROR_TYPES.SERVER:
      return 'Server Error';
    default:
      return 'Error';
  }
};

/**
 * Check if error is retryable
 */
export const isRetryableError = (error) => {
  // Network errors are retryable
  if (!error.response) return true;
  
  const status = error.response.status;
  
  // Server errors (5xx) are retryable
  if (status >= 500) return true;
  
  // Rate limiting (429) is retryable
  if (status === 429) return true;
  
  // Client errors (4xx) are not retryable
  return false;
};

/**
 * Safely extract error message for React rendering
 * Ensures the returned value is always a string, never an object
 */
export const getErrorMessage = (error, fallback = 'An error occurred') => {
  if (!error) return fallback;

  // If error is already a string, return it
  if (typeof error === 'string') return error;

  // If error is an object, try to extract message safely
  if (typeof error === 'object') {
    // Try different common error message properties
    const possibleMessages = [
      error.message,
      error.detail,
      error.error,
      error.msg
    ];

    for (const msg of possibleMessages) {
      if (typeof msg === 'string' && msg.trim()) {
        return msg;
      }
      // If the property exists but is an object, stringify it
      if (msg && typeof msg === 'object') {
        try {
          return JSON.stringify(msg);
        } catch {
          // If JSON.stringify fails, continue to next option
          continue;
        }
      }
    }

    // If no string message found, stringify the entire error object
    try {
      return JSON.stringify(error);
    } catch {
      return fallback;
    }
  }

  // For any other type, convert to string
  return String(error);
};

/**
 * Format error for display in UI
 */
export const formatErrorForDisplay = (error) => {
  if (typeof error === 'string') {
    return { title: 'Error', message: error };
  }

  if (error.type) {
    return {
      title: getErrorTitle(error.type),
      message: getErrorMessage(error),
      details: error.fieldErrors,
    };
  }

  return {
    title: 'Error',
    message: getErrorMessage(error),
  };
};

export default {
  handleApiError,
  handleValidationError,
  withErrorHandling,
  createErrorBoundaryHandler,
  showErrorToast,
  formatErrorForDisplay,
  isRetryableError,
  ERROR_TYPES,
};
