import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiArrowLeft,
  FiStar,
  FiMapPin,
  FiClock,
  FiDollarSign,
  FiAward,
  FiCheckCircle,
  FiMessageCircle,
  FiGlobe,
  FiExternalLink,
  FiCalendar,
  FiUser
} from 'react-icons/fi';
import {
  fetchMentorDetails,
  selectCurrentMentor,
  selectMentorDetailsLoading,
  selectMentorDetailsError
} from '../../store/slices/MentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const MentorDetailsPage = () => {
  const { mentorId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('overview');
  const [showContactModal, setShowContactModal] = useState(false);

  // Redux state
  const mentor = useSelector(selectCurrentMentor);
  const loading = useSelector(selectMentorDetailsLoading);
  const error = useSelector(selectMentorDetailsError);

  // Load mentor details on mount
  useEffect(() => {
    if (mentorId) {
      dispatch(fetchMentorDetails(mentorId));
    }
  }, [dispatch, mentorId]);

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <FiStar key={i} className="h-4 w-4 text-yellow-400 fill-current" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <FiStar key="half" className="h-4 w-4 text-yellow-400 fill-current opacity-50" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <FiStar key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
      );
    }

    return stars;
  };

  const handleContact = () => {
    setShowContactModal(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  if (!mentor) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Mentor not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The mentor you're looking for doesn't exist or is no longer available.
        </p>
      </div>
    );
  }

  // Handle both old and new mentor data structures
  const mentorData = mentor.user ? mentor.user : mentor;
  const profileData = mentor.profile || mentor.user?.mentor_profile || mentor;

  // Extract data with fallbacks
  const mentorInfo = {
    id: mentorData.id,
    username: mentorData.username,
    full_name: mentorData.full_name || mentorData.username,
    email: mentorData.email,
    mobile: mentorData.mobile,
    country: mentorData.country,
    profile_picture: mentorData.profile_picture,
    user_type: mentorData.user_type,
    is_email_verified: mentorData.is_email_verified,
    is_mobile_verified: mentorData.is_mobile_verified,

    // Profile data
    bio: profileData.bio,
    experience_years: profileData.experience_years,
    hourly_rate: profileData.hourly_rate,
    languages: profileData.languages || [],
    availability_hours: profileData.availability_hours || {},
    profile_image_url: profileData.profile_image_url || mentorData.profile_picture,
    expertise_subjects: profileData.expertise_subjects || [],
    preferred_subjects: profileData.preferred_subjects || [],

    // Additional fields
    total_competitions: mentor.total_competitions,
    active_institutes: mentor.active_institutes,
    average_rating: mentor.average_rating,
    verification_status: mentor.verification_status,

    // Computed fields for backward compatibility
    rating: mentor.average_rating || mentor.rating || 0,
    total_reviews: mentor.total_reviews || 0,
    is_verified: mentor.verification_status === 'verified' || mentor.is_verified,
    is_available: mentor.is_available !== false // Default to true if not specified
  };

  const handleBookSession = () => {
    // Navigate to booking page or open booking modal
    console.log('Book session with mentor:', mentorInfo);
  };

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Back Button */}
      <button
        onClick={() => navigate(-1)}
        className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-6"
      >
        <FiArrowLeft className="h-4 w-4 mr-2" />
        Back to Mentors
      </button>

      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
        <div className="p-6">
          <div className="flex items-start space-x-6">
            {/* Profile Image */}
            <div className="relative">
              <img
                src={mentorInfo.profile_image_url || '/default-avatar.png'}
                alt={mentorInfo.full_name}
                className="w-24 h-24 rounded-full object-cover"
              />
              {mentorInfo.is_verified && (
                <div className="absolute -top-1 -right-1">
                  <FiCheckCircle className="h-8 w-8 text-green-500 bg-white rounded-full" />
                </div>
              )}
            </div>

            {/* Basic Info */}
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">{mentorInfo.full_name}</h1>
                  <p className="text-gray-600 mt-1">@{mentorInfo.username}</p>
                </div>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  mentorInfo.is_available
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    mentorInfo.is_available ? 'bg-green-400' : 'bg-gray-400'
                  }`} />
                  {mentorInfo.is_available ? 'Available' : 'Busy'}
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center space-x-4 mt-3">
                <div className="flex items-center">
                  {renderStars(mentorInfo.rating)}
                  <span className="ml-2 text-sm text-gray-600">
                    {mentorInfo.rating.toFixed(1)} ({mentorInfo.total_reviews} reviews)
                  </span>
                </div>
                {mentorInfo.is_verified && (
                  <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <FiAward className="h-3 w-3 mr-1" />
                    Verified
                  </div>
                )}
              </div>

              {/* Quick Stats */}
              <div className="flex items-center space-x-6 mt-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <FiMapPin className="h-4 w-4 mr-1" />
                  {mentorInfo.country}
                </div>
                <div className="flex items-center">
                  <FiClock className="h-4 w-4 mr-1" />
                  {mentorInfo.experience_years} years exp.
                </div>
                <div className="flex items-center">
                  <FiDollarSign className="h-4 w-4 mr-1" />
                  ${mentorInfo.hourly_rate}/hr
                </div>
                {mentorInfo.languages && mentorInfo.languages.length > 0 && (
                  <div className="flex items-center">
                    <FiGlobe className="h-4 w-4 mr-1" />
                    {mentorInfo.languages.join(', ')}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-3 mt-6">
                {mentorInfo.is_available && (
                  <button
                    onClick={handleBookSession}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <FiCalendar className="h-4 w-4 mr-2" />
                    Book Session
                  </button>
                )}
                <button
                  onClick={handleContact}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <FiMessageCircle className="h-4 w-4 mr-2" />
                  Contact
                </button>
                {mentorInfo.portfolio_url && (
                  <a
                    href={mentorInfo.portfolio_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <FiExternalLink className="h-4 w-4 mr-2" />
                    Portfolio
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Overview' },
              { id: 'expertise', name: 'Expertise' },
              { id: 'reviews', name: 'Reviews' },
              { id: 'availability', name: 'Availability' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {activeTab === 'overview' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">About</h2>
              <p className="text-gray-600 leading-relaxed">{mentorInfo.bio}</p>

              {mentorInfo.teaching_style && (
                <div className="mt-6">
                  <h3 className="text-md font-medium text-gray-900 mb-2">Teaching Style</h3>
                  <p className="text-gray-600">{mentorInfo.teaching_style}</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'expertise' && (
            <div className="space-y-6">
              {/* Expertise Areas */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Expertise Areas</h2>
                <div className="flex flex-wrap gap-2">
                  {mentorInfo.expertise_subjects?.map((subject, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                    >
                      {subject.name || subject}
                    </span>
                  ))}
                </div>
              </div>

              {/* Preferred Subjects */}
              {mentorInfo.preferred_subjects && mentorInfo.preferred_subjects.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Preferred Subjects</h2>
                  <div className="flex flex-wrap gap-2">
                    {mentorInfo.preferred_subjects.map((subject, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                      >
                        {subject.name || subject}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Languages */}
              {mentorInfo.languages && mentorInfo.languages.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Languages</h2>
                  <div className="flex flex-wrap gap-2">
                    {mentorInfo.languages.map((language, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800"
                      >
                        {language}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'reviews' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Reviews</h2>
              <div className="text-center py-8">
                <FiUser className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews yet</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Be the first to leave a review for this mentor.
                </p>
              </div>
            </div>
          )}

          {activeTab === 'availability' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Availability</h2>
              {mentorInfo.availability_hours && Object.keys(mentorInfo.availability_hours).length > 0 ? (
                <div className="space-y-3">
                  {Object.entries(mentorInfo.availability_hours).map(([day, hours]) => (
                    <div key={day} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                      <span className="font-medium text-gray-900 capitalize">{day}</span>
                      {Array.isArray(hours) && hours.length > 0 ? (
                        <span className="text-green-600">
                          {hours.join(', ')}
                        </span>
                      ) : hours === 'OFF' ? (
                        <span className="text-gray-500">Not available</span>
                      ) : (
                        <span className="text-green-600">{hours}</span>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">Availability information not provided.</p>
              )}
              <div className="mt-4 text-sm text-gray-500">
                <p>Timezone: {mentorInfo.timezone || 'Not specified'}</p>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Response Rate</span>
                <span className="font-medium text-gray-900">95%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Response Time</span>
                <span className="font-medium text-gray-900">< 2 hours</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Sessions Completed</span>
                <span className="font-medium text-gray-900">150+</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Student Satisfaction</span>
                <span className="font-medium text-gray-900">98%</span>
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact</h3>
            <div className="space-y-3">
              {mentorInfo.linkedin_url && (
                <a
                  href={mentorInfo.linkedin_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-blue-600 hover:text-blue-800"
                >
                  <FiExternalLink className="h-4 w-4 mr-2" />
                  LinkedIn Profile
                </a>
              )}
              {mentorInfo.portfolio_url && (
                <a
                  href={mentorInfo.portfolio_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-blue-600 hover:text-blue-800"
                >
                  <FiExternalLink className="h-4 w-4 mr-2" />
                  Portfolio
                </a>
              )}
            </div>
          </div>

          {/* Similar Mentors */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Similar Mentors</h3>
            <div className="text-center py-4">
              <p className="text-sm text-gray-500">
                Discover other mentors with similar expertise
              </p>
              <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">
                View Similar Mentors
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Modal */}
      {showContactModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Contact {mentorInfo.full_name}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subject
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="What would you like to discuss?"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Message
                  </label>
                  <textarea
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tell the mentor about your goals and what you're looking for..."
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowContactModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setShowContactModal(false)}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Send Message
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MentorDetailsPage;
